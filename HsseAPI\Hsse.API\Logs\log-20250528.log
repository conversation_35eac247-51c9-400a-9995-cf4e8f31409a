2025-05-28 12:43:21.691 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-28 12:43:23.181 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-28 12:43:23.241 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-28 12:43:23.583 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-28 12:43:23.586 +05:30 [INF] Hosting environment: Development
2025-05-28 12:43:23.588 +05:30 [INF] Content root path: C:\Users\<USER>\Desktop\Codebase\SUSTAINEDGE\store-inventory-mobile-api\HsseAPI\Hsse.API
2025-05-28 12:43:37.705 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-28 12:43:38.334 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 659.8806ms
2025-05-28 12:43:38.422 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-28 12:43:38.474 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-28 12:43:38.503 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13788 application/javascript; charset=utf-8 80.5817ms
2025-05-28 12:43:39.276 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 802.0712ms
2025-05-28 12:43:39.416 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-28 12:43:39.548 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 132.0656ms
2025-05-28 12:44:38.928 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Cart/GetCartDetailsByUserId?facilityCode=AH%20HSK - null null
2025-05-28 12:44:39.619 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API)'
2025-05-28 12:44:39.807 +05:30 [INF] Route matched with {action = "GetCartDetailsByUserId", controller = "Cart"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetCartDetailsByUserId(Int32, System.String) on controller Hsse.API.Controllers.v1.CartController (Hsse.API).
2025-05-28 12:44:41.609 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-05-28 12:44:41.623 +05:30 [INF] Executed action Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API) in 1791.0735ms
2025-05-28 12:44:41.626 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API)'
2025-05-28 12:44:41.629 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Cart/GetCartDetailsByUserId?facilityCode=AH%20HSK - 200 null application/json; charset=utf-8 2700.9886ms
2025-05-28 12:44:46.291 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Cart/GetCartDetailsByUserId?locationId=5&facilityCode=AH%20HSK - null null
2025-05-28 12:44:46.432 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API)'
2025-05-28 12:44:46.450 +05:30 [INF] Route matched with {action = "GetCartDetailsByUserId", controller = "Cart"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetCartDetailsByUserId(Int32, System.String) on controller Hsse.API.Controllers.v1.CartController (Hsse.API).
2025-05-28 12:44:46.630 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-05-28 12:44:46.676 +05:30 [INF] Executed action Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API) in 223.0428ms
2025-05-28 12:44:46.680 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API)'
2025-05-28 12:44:46.684 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Cart/GetCartDetailsByUserId?locationId=5&facilityCode=AH%20HSK - 200 null application/json; charset=utf-8 393.2547ms
2025-05-28 12:45:12.457 +05:30 [INF] Request starting HTTP/2 PUT https://localhost:7231/api/v1/Cart/UpdateItemsByCart - application/json 48
2025-05-28 12:45:12.474 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.CartController.UpdateItemsByCart (Hsse.API)'
2025-05-28 12:45:12.485 +05:30 [INF] Route matched with {action = "UpdateItemsByCart", controller = "Cart"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult UpdateItemsByCart(Hsse.Data.Dto.Request.UpdateCartProductDto) on controller Hsse.API.Controllers.v1.CartController (Hsse.API).
2025-05-28 12:45:12.592 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-05-28 12:45:12.596 +05:30 [INF] Executed action Hsse.API.Controllers.v1.CartController.UpdateItemsByCart (Hsse.API) in 106.7701ms
2025-05-28 12:45:12.599 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.CartController.UpdateItemsByCart (Hsse.API)'
2025-05-28 12:45:12.601 +05:30 [INF] Request finished HTTP/2 PUT https://localhost:7231/api/v1/Cart/UpdateItemsByCart - 200 null application/json; charset=utf-8 144.5386ms
2025-05-28 12:45:15.833 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Cart/GetCartDetailsByUserId?locationId=5&facilityCode=AH%20HSK - null null
2025-05-28 12:45:15.840 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API)'
2025-05-28 12:45:15.904 +05:30 [INF] Route matched with {action = "GetCartDetailsByUserId", controller = "Cart"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetCartDetailsByUserId(Int32, System.String) on controller Hsse.API.Controllers.v1.CartController (Hsse.API).
2025-05-28 12:45:15.982 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-05-28 12:45:15.985 +05:30 [INF] Executed action Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API) in 54.232ms
2025-05-28 12:45:15.988 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API)'
2025-05-28 12:45:15.992 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Cart/GetCartDetailsByUserId?locationId=5&facilityCode=AH%20HSK - 200 null application/json; charset=utf-8 158.8522ms
2025-05-28 12:46:06.134 +05:30 [INF] Request starting HTTP/2 POST https://localhost:7231/api/v1/Cart/CreateCartOrder - application/json 190
2025-05-28 12:46:06.149 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.CartController.CreateCartOrder (Hsse.API)'
2025-05-28 12:46:06.155 +05:30 [INF] Route matched with {action = "CreateCartOrder", controller = "Cart"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult CreateCartOrder(Hsse.Data.Dto.Request.CartOrderRequestDto) on controller Hsse.API.Controllers.v1.CartController (Hsse.API).
2025-05-28 12:46:07.322 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-05-28 12:46:07.334 +05:30 [INF] Executed action Hsse.API.Controllers.v1.CartController.CreateCartOrder (Hsse.API) in 1174.6209ms
2025-05-28 12:46:07.339 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.CartController.CreateCartOrder (Hsse.API)'
2025-05-28 12:46:07.343 +05:30 [INF] Request finished HTTP/2 POST https://localhost:7231/api/v1/Cart/CreateCartOrder - 200 null application/json; charset=utf-8 1209.3512ms
2025-05-28 12:46:17.130 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Cart/GetCartDetailsByUserId?locationId=5&facilityCode=AH%20HSK - null null
2025-05-28 12:46:17.136 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API)'
2025-05-28 12:46:17.152 +05:30 [INF] Route matched with {action = "GetCartDetailsByUserId", controller = "Cart"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetCartDetailsByUserId(Int32, System.String) on controller Hsse.API.Controllers.v1.CartController (Hsse.API).
2025-05-28 12:46:17.217 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-05-28 12:46:17.228 +05:30 [INF] Executed action Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API) in 60.3689ms
2025-05-28 12:46:17.231 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.CartController.GetCartDetailsByUserId (Hsse.API)'
2025-05-28 12:46:17.234 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Cart/GetCartDetailsByUserId?locationId=5&facilityCode=AH%20HSK - 200 null application/json; charset=utf-8 104.2448ms
