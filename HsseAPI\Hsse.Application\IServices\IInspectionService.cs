﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.IServices
{
    public interface IInspectionService
    {
        long CreateInspection(CreateInspectionDto createInspectionDto);
        int VerifyActionParty(VerifyActionPartyDto verifyActionPartyDto);
        int VerifyInspector(VerifyInspectorDto verifyInspectorDto);
        List<MstInspections> GetInspections();
        List<MstActionParty> GetActionParties();
    }
}
