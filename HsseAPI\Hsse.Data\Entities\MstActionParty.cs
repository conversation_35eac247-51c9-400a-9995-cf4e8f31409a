﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstActionParty
    {
        [Key]
        public int ActionPartyId { get; set; }
        public string? name { get; set; }
        public DateTime? created_at { get; set; }
        public int? created_by { get; set; }
        public DateTime? modified_at { get; set; }
        public int? modified_by { get; set; }
        public bool IsActive { get; set; }
        public int? FacilityID { get; set; }
        public string? Observation { get; set; }
        public string? ObservationMediaUrl { get; set; }
        public string? RecommendationMediaUrl { get; set; }
    }
}
