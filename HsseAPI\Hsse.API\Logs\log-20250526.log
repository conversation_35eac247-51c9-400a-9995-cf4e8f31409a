2025-05-26 10:47:50.201 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-26 10:47:51.362 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-26 10:47:51.474 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-26 10:47:51.725 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-26 10:47:51.732 +05:30 [INF] Hosting environment: Development
2025-05-26 10:47:51.733 +05:30 [INF] Content root path: C:\Users\<USER>\Desktop\Codebase\SUSTAINEDGE\store-inventory-mobile-api\HsseAPI\Hsse.API
2025-05-26 10:48:01.331 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-26 10:48:01.976 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 657.2022ms
2025-05-26 10:48:02.234 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-26 10:48:02.283 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-26 10:48:02.311 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13788 application/javascript; charset=utf-8 77.964ms
2025-05-26 10:48:02.861 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 578.339ms
2025-05-26 10:48:03.003 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-26 10:48:03.069 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 65.5629ms
2025-05-26 10:48:25.170 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Order/GetItemsByOrderId?orderId=1 - null null
2025-05-26 10:48:25.505 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.OrderController.GetItemsByOrderId (Hsse.API)'
2025-05-26 10:48:25.569 +05:30 [INF] Route matched with {action = "GetItemsByOrderId", controller = "Order"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetItemsByOrderId(Int32) on controller Hsse.API.Controllers.v1.OrderController (Hsse.API).
2025-05-26 10:48:27.139 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-05-26 10:48:27.173 +05:30 [INF] Executed action Hsse.API.Controllers.v1.OrderController.GetItemsByOrderId (Hsse.API) in 1593.8532ms
2025-05-26 10:48:27.175 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.OrderController.GetItemsByOrderId (Hsse.API)'
2025-05-26 10:48:27.177 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Order/GetItemsByOrderId?orderId=1 - 200 null application/json; charset=utf-8 2007.2917ms
2025-05-26 10:49:04.447 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Order/GetItemsByOrderId?orderId=1 - null null
2025-05-26 10:49:04.472 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.OrderController.GetItemsByOrderId (Hsse.API)'
2025-05-26 10:49:04.503 +05:30 [INF] Route matched with {action = "GetItemsByOrderId", controller = "Order"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetItemsByOrderId(Int32) on controller Hsse.API.Controllers.v1.OrderController (Hsse.API).
2025-05-26 10:50:31.722 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-05-26 10:50:31.750 +05:30 [INF] Executed action Hsse.API.Controllers.v1.OrderController.GetItemsByOrderId (Hsse.API) in 87212.5207ms
2025-05-26 10:50:31.756 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.OrderController.GetItemsByOrderId (Hsse.API)'
2025-05-26 10:50:31.759 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Order/GetItemsByOrderId?orderId=1 - 200 null application/json; charset=utf-8 87312.4045ms
2025-05-26 10:51:21.435 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-26 10:51:25.486 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-26 10:51:25.490 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-26 10:51:26.196 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-26 10:51:26.497 +05:30 [INF] Hosting environment: Development
2025-05-26 10:51:26.503 +05:30 [INF] Content root path: C:\Users\<USER>\Desktop\Codebase\SUSTAINEDGE\store-inventory-mobile-api\HsseAPI\Hsse.API
2025-05-26 10:51:34.497 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-26 10:51:35.060 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 584.4462ms
2025-05-26 10:51:35.136 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-26 10:51:35.136 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-26 10:51:35.157 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13788 application/javascript; charset=utf-8 20.6779ms
2025-05-26 10:51:35.240 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 103.5701ms
2025-05-26 10:51:35.316 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-26 10:51:35.457 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 142.0711ms
2025-05-26 10:53:04.282 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Order/GetItemsByOrderId?orderId=1 - null null
2025-05-26 10:53:04.734 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.OrderController.GetItemsByOrderId (Hsse.API)'
2025-05-26 10:53:04.796 +05:30 [INF] Route matched with {action = "GetItemsByOrderId", controller = "Order"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetItemsByOrderId(Int32) on controller Hsse.API.Controllers.v1.OrderController (Hsse.API).
2025-05-26 10:53:12.024 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-05-26 10:53:12.079 +05:30 [INF] Executed action Hsse.API.Controllers.v1.OrderController.GetItemsByOrderId (Hsse.API) in 7269.4236ms
2025-05-26 10:53:12.081 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.OrderController.GetItemsByOrderId (Hsse.API)'
2025-05-26 10:53:12.083 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Order/GetItemsByOrderId?orderId=1 - 200 null application/json; charset=utf-8 7801.9095ms
2025-05-26 10:53:42.143 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Order/GetAllOrderDetails?facilityCode=AH%20HSK - null null
2025-05-26 10:53:42.171 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.OrderController.GetAllOrderDetails (Hsse.API)'
2025-05-26 10:53:42.195 +05:30 [INF] Route matched with {action = "GetAllOrderDetails", controller = "Order"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAllOrderDetails(System.String, System.String, System.Nullable`1[System.Int32], System.Nullable`1[System.Int32], System.Nullable`1[System.Int32], System.Nullable`1[System.Int32], System.String, System.String) on controller Hsse.API.Controllers.v1.OrderController (Hsse.API).
2025-05-26 10:53:43.379 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-05-26 10:53:43.421 +05:30 [INF] Executed action Hsse.API.Controllers.v1.OrderController.GetAllOrderDetails (Hsse.API) in 1221.9512ms
2025-05-26 10:53:43.426 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.OrderController.GetAllOrderDetails (Hsse.API)'
2025-05-26 10:53:43.431 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Order/GetAllOrderDetails?facilityCode=AH%20HSK - 200 null application/json; charset=utf-8 1287.7788ms
