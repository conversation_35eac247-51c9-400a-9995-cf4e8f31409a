﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Request
{
    public class CreatePostDto
    {
        public int PostID { get; set; }
        public int UserID { get; set; }
        public int? FacilityID { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string PostType { get; set; }
        public string Location { get; set; }
        public int? TaggedCategoryId { get; set; }
        public string RequiresFollowup { get; set; }
        public int? Status { get; set; }
        public int MediaID { get; set; }
        public string MediaURL { get; set; }
    }
}
