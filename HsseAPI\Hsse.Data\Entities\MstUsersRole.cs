﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstUsersRole
    {
        [Key]
        public int RoleID { get; set; }
        public string? RoleName { get; set; }
        public bool? SeededRole { get; set; }
        public DateTime? CreatedAt { get; set; }
        public int? CreatedBy { get; set; }
        public bool IsActive { get; set; } = true;
    }

}
