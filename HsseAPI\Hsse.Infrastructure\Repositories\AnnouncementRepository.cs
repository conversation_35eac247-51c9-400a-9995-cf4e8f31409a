﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class AnnouncementRepository : IAnnouncementRepository
    {
        private readonly MasterDBContext _MasterDBContext;

        public AnnouncementRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }

        public long CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto)
        {
            if (createAnnouncementDto.AnnouncementsId == 0)
            {
                // Create new announcement
                var newAnnouncement = new MstAnnouncements
                {
                    Title = createAnnouncementDto.Title,
                    Description = createAnnouncementDto.Description,
                    Status = createAnnouncementDto.Status,
                    Created_by = createAnnouncementDto.CreatedBy,
                    Created_at = DateTime.Now,
                    AnnouncementDocument = createAnnouncementDto.AnnouncementDocument,
                    ScheduleAt = createAnnouncementDto.ScheduleAt,
                    ExpiryAt = createAnnouncementDto.ExpiryAt,
                    FacilityID = createAnnouncementDto.FacilityID,
                    CategoryId = createAnnouncementDto.CategoryId
                };

                _MasterDBContext.MstAnnouncements.Add(newAnnouncement);
                _MasterDBContext.SaveChanges();

                // Add receivers
                foreach (var userId in createAnnouncementDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        AnnouncementID = newAnnouncement.Announcements_id,
                        UserID = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createAnnouncementDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        AnnouncementID = newAnnouncement.Announcements_id,
                        GroupID = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                // Add documents
                foreach (var doc in createAnnouncementDto.Documents)
                {
                    var document = new MstAnnouncementDocuments
                    {
                        AnnouncementID = newAnnouncement.Announcements_id,
                        DocumentName = doc.DocumentName,
                        DocumentFile = doc.DocumentFile
                    };
                    _MasterDBContext.MstAnnouncementDocuments.Add(document);
                }

                _MasterDBContext.SaveChanges();
                return newAnnouncement.Announcements_id;
            }
            else
            {
                // Update existing announcement
                var existingAnnouncement = _MasterDBContext.MstAnnouncements
                    .FirstOrDefault(a => a.Announcements_id == createAnnouncementDto.AnnouncementsId);

                if (existingAnnouncement == null)
                    return 0;

                existingAnnouncement.Title = createAnnouncementDto.Title;
                existingAnnouncement.Description = createAnnouncementDto.Description;
                existingAnnouncement.Status = createAnnouncementDto.Status;
                existingAnnouncement.Modified_by = createAnnouncementDto.ModifiedBy;
                existingAnnouncement.Modified_at = DateTime.Now;
                existingAnnouncement.AnnouncementDocument = createAnnouncementDto.AnnouncementDocument;
                existingAnnouncement.ScheduleAt = createAnnouncementDto.ScheduleAt;
                existingAnnouncement.ExpiryAt = createAnnouncementDto.ExpiryAt;
                existingAnnouncement.FacilityID = createAnnouncementDto.FacilityID;
                existingAnnouncement.CategoryId = createAnnouncementDto.CategoryId;

                // Remove existing receivers and documents, then add new ones
                var existingReceivers = _MasterDBContext.MstAnnouncementReceivers
                    .Where(r => r.AnnouncementID == createAnnouncementDto.AnnouncementsId);
                _MasterDBContext.MstAnnouncementReceivers.RemoveRange(existingReceivers);

                var existingDocuments = _MasterDBContext.MstAnnouncementDocuments
                    .Where(d => d.AnnouncementID == createAnnouncementDto.AnnouncementsId);
                _MasterDBContext.MstAnnouncementDocuments.RemoveRange(existingDocuments);

                // Add new receivers
                foreach (var userId in createAnnouncementDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        AnnouncementID = createAnnouncementDto.AnnouncementsId,
                        UserID = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createAnnouncementDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        AnnouncementID = createAnnouncementDto.AnnouncementsId,
                        GroupID = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                // Add new documents
                foreach (var doc in createAnnouncementDto.Documents)
                {
                    var document = new MstAnnouncementDocuments
                    {
                        AnnouncementID = createAnnouncementDto.AnnouncementsId,
                        DocumentName = doc.DocumentName,
                        DocumentFile = doc.DocumentFile
                    };
                    _MasterDBContext.MstAnnouncementDocuments.Add(document);
                }

                _MasterDBContext.SaveChanges();
                return existingAnnouncement.Announcements_id;
            }
        }

        public int CreateOrUpdateCategory(CreateAnnouncementCategoryDto createCategoryDto)
        {
            if (createCategoryDto.AnnoucementCategoryId == 0)
            {
                // Create new category
                var newCategory = new MstAnnouncementCategory
                {
                    AnnoucementCategoryName = createCategoryDto.AnnoucementCategoryName,
                    Status = createCategoryDto.Status,
                    CreatedBy = createCategoryDto.CreatedBy,
                    CreatedAt = DateTime.Now
                };

                _MasterDBContext.MstAnnouncementCategory.Add(newCategory);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }
            else
            {
                // Update existing category
                var existingCategory = _MasterDBContext.MstAnnouncementCategory
                    .FirstOrDefault(c => c.AnnoucementCategoryId == createCategoryDto.AnnoucementCategoryId);

                if (existingCategory == null)
                    return 0; // Category not found

                existingCategory.AnnoucementCategoryName = createCategoryDto.AnnoucementCategoryName;
                existingCategory.Status = createCategoryDto.Status;
                existingCategory.ModifiedBy = createCategoryDto.ModifiedBy;
                existingCategory.ModifiedAt = DateTime.Now;

                _MasterDBContext.SaveChanges();
                return 2; // Updated
            }
        }

        public List<MstAnnouncementCategory> GetCategories()
        {
            return _MasterDBContext.MstAnnouncementCategory.ToList();
        }

        public List<MstAnnouncements> GetAnnouncements()
        {
            return _MasterDBContext.MstAnnouncements.ToList();
        }
    }
}
