using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class AnnouncementRepository : IAnnouncementRepository
    {
        private readonly MasterDBContext _MasterDBContext;

        public AnnouncementRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }

        public long CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto)
        {
            if (createAnnouncementDto.AnnouncementsId == 0)
            {
                // Create new announcement
                var newAnnouncement = new MstAnnouncements
                {
                    Title = createAnnouncementDto.Title,
                    Description = createAnnouncementDto.Description,
                    Status = createAnnouncementDto.Status,
                    Created_by = createAnnouncementDto.CreatedBy,
                    Created_at = DateTime.Now,
                    AnnouncementDocument = createAnnouncementDto.AnnouncementDocument,
                    ScheduleAt = createAnnouncementDto.ScheduleAt,
                    ExpiryAt = createAnnouncementDto.ExpiryAt,
                    FacilityID = createAnnouncementDto.FacilityID,
                    CategoryId = createAnnouncementDto.CategoryId
                };

                _MasterDBContext.MstAnnouncements.Add(newAnnouncement);
                _MasterDBContext.SaveChanges();

                // Add receivers
                foreach (var userId in createAnnouncementDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        AnnouncementID = newAnnouncement.Announcements_id,
                        UserID = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createAnnouncementDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        AnnouncementID = newAnnouncement.Announcements_id,
                        GroupID = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                // Add documents
                foreach (var doc in createAnnouncementDto.Documents)
                {
                    var document = new MstAnnouncementDocuments
                    {
                        AnnouncementID = newAnnouncement.Announcements_id,
                        DocumentName = doc.DocumentName,
                        DocumentFile = doc.DocumentFile
                    };
                    _MasterDBContext.MstAnnouncementDocuments.Add(document);
                }

                _MasterDBContext.SaveChanges();
                return newAnnouncement.Announcements_id;
            }
            else
            {
                // Update existing announcement
                var existingAnnouncement = _MasterDBContext.MstAnnouncements
                    .FirstOrDefault(a => a.Announcements_id == createAnnouncementDto.AnnouncementsId);

                if (existingAnnouncement == null)
                    return 0;

                existingAnnouncement.Title = createAnnouncementDto.Title;
                existingAnnouncement.Description = createAnnouncementDto.Description;
                existingAnnouncement.Status = createAnnouncementDto.Status;
                existingAnnouncement.Modified_by = createAnnouncementDto.ModifiedBy;
                existingAnnouncement.Modified_at = DateTime.Now;
                existingAnnouncement.AnnouncementDocument = createAnnouncementDto.AnnouncementDocument;
                existingAnnouncement.ScheduleAt = createAnnouncementDto.ScheduleAt;
                existingAnnouncement.ExpiryAt = createAnnouncementDto.ExpiryAt;
                existingAnnouncement.FacilityID = createAnnouncementDto.FacilityID;
                existingAnnouncement.CategoryId = createAnnouncementDto.CategoryId;

                // Remove existing receivers and documents, then add new ones
                var existingReceivers = _MasterDBContext.MstAnnouncementReceivers
                    .Where(r => r.AnnouncementID == createAnnouncementDto.AnnouncementsId);
                _MasterDBContext.MstAnnouncementReceivers.RemoveRange(existingReceivers);

                var existingDocuments = _MasterDBContext.MstAnnouncementDocuments
                    .Where(d => d.AnnouncementID == createAnnouncementDto.AnnouncementsId);
                _MasterDBContext.MstAnnouncementDocuments.RemoveRange(existingDocuments);

                // Add new receivers
                foreach (var userId in createAnnouncementDto.ReceiverUserIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        AnnouncementID = createAnnouncementDto.AnnouncementsId,
                        UserID = userId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                foreach (var groupId in createAnnouncementDto.ReceiverGroupIds)
                {
                    var receiver = new MstAnnouncementReceivers
                    {
                        AnnouncementID = createAnnouncementDto.AnnouncementsId,
                        GroupID = groupId,
                        Delivered = false
                    };
                    _MasterDBContext.MstAnnouncementReceivers.Add(receiver);
                }

                // Add new documents
                foreach (var doc in createAnnouncementDto.Documents)
                {
                    var document = new MstAnnouncementDocuments
                    {
                        AnnouncementID = createAnnouncementDto.AnnouncementsId,
                        DocumentName = doc.DocumentName,
                        DocumentFile = doc.DocumentFile
                    };
                    _MasterDBContext.MstAnnouncementDocuments.Add(document);
                }

                _MasterDBContext.SaveChanges();
                return existingAnnouncement.Announcements_id;
            }
        }

        public int CreateOrUpdateCategory(CreateAnnouncementCategoryDto createCategoryDto)
        {
            if (createCategoryDto.AnnoucementCategoryId == 0)
            {
                // Create new category
                var newCategory = new MstAnnouncementCategory
                {
                    AnnoucementCategoryName = createCategoryDto.AnnoucementCategoryName,
                    Status = createCategoryDto.Status,
                    CreatedBy = createCategoryDto.CreatedBy,
                    CreatedAt = DateTime.Now
                };

                _MasterDBContext.MstAnnouncementCategory.Add(newCategory);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }
            else
            {
                // Update existing category
                var existingCategory = _MasterDBContext.MstAnnouncementCategory
                    .FirstOrDefault(c => c.AnnoucementCategoryId == createCategoryDto.AnnoucementCategoryId);

                if (existingCategory == null)
                    return 0; // Category not found

                existingCategory.AnnoucementCategoryName = createCategoryDto.AnnoucementCategoryName;
                existingCategory.Status = createCategoryDto.Status;
                existingCategory.ModifiedBy = createCategoryDto.ModifiedBy;
                existingCategory.ModifiedAt = DateTime.Now;

                _MasterDBContext.SaveChanges();
                return 2; // Updated
            }
        }

        public List<MstAnnouncementCategory> GetCategories()
        {
            return _MasterDBContext.MstAnnouncementCategory.ToList();
        }

        public List<MstAnnouncements> GetAnnouncements()
        {
            return _MasterDBContext.MstAnnouncements.ToList();
        }

        public int AcknowledgeAnnouncement(AcknowledgeAnnouncementDto acknowledgeDto)
        {
            var existingAcknowledgment = _MasterDBContext.MstAnnouncementAcknowledgments
                .FirstOrDefault(a => a.AnnouncementID == acknowledgeDto.AnnouncementID &&
                                   a.UserID == acknowledgeDto.UserID);

            if (existingAcknowledgment == null)
            {
                // Create new acknowledgment
                var newAcknowledgment = new MstAnnouncementAcknowledgments
                {
                    AnnouncementID = acknowledgeDto.AnnouncementID,
                    UserID = acknowledgeDto.UserID,
                    IsAcknowledged = acknowledgeDto.IsAcknowledged,
                    AcknowledgedAt = DateTime.Now
                };

                _MasterDBContext.MstAnnouncementAcknowledgments.Add(newAcknowledgment);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }
            else
            {
                // Update existing acknowledgment
                existingAcknowledgment.IsAcknowledged = acknowledgeDto.IsAcknowledged;
                existingAcknowledgment.AcknowledgedAt = DateTime.Now;

                _MasterDBContext.SaveChanges();
                return 2; // Updated
            }
        }

        public int ReactToAnnouncement(ReactToAnnouncementDto reactDto)
        {
            // Use the existing MstLikesConfig table but extend it for announcements
            // We need to add AnnouncementID field to MstLikesConfig or create a new reaction table
            // For now, I'll create a simple reaction using the existing likes structure
            // This would require adding AnnouncementID to MstLikesConfig entity

            // For this implementation, I'll use a notification approach
            var notification = new MstNotifications
            {
                UserID = reactDto.UserID,
                Heading = "Announcement Reaction",
                Message = $"User reacted to announcement {reactDto.AnnouncementID} with {reactDto.ReactionType}",
                IsRead = false,
                CreatedAt = DateTime.Now
            };

            _MasterDBContext.MstNotifications.Add(notification);
            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public int RegisterForEvent(RegisterForEventDto registerDto)
        {
            var existingResponse = _MasterDBContext.MstEventResponses
                .FirstOrDefault(r => r.EventID == registerDto.EventID &&
                               r.UserID == registerDto.UserID);

            if (existingResponse == null)
            {
                // Create new event response
                var newResponse = new MstEventResponses
                {
                    EventID = registerDto.EventID,
                    UserID = registerDto.UserID,
                    IsAccepted = registerDto.IsAccepted,
                    RespondedAt = DateTime.Now
                };

                _MasterDBContext.MstEventResponses.Add(newResponse);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }
            else
            {
                // Update existing response
                existingResponse.IsAccepted = registerDto.IsAccepted;
                existingResponse.RespondedAt = DateTime.Now;

                _MasterDBContext.SaveChanges();
                return 2; // Updated
            }
        }
    }
}
