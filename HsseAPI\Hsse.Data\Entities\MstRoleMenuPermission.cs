﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstRoleMenuPermission
    {
        [Key]
        public int RoleMenuPermissionsId { get; set; }
        public int? RoleId { get; set; }
        public int PermissionID { get; set; }
        public bool? CanView { get; set; } = false;
        public bool? CanCreate { get; set; } = false;
        public bool? CanEdit { get; set; } = false;
        public bool? CanDelete { get; set; } = false;
    }

}
