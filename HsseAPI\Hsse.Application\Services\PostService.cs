﻿using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class PostService : IPostService
    {
        private readonly IPostRepository _IPostRepository;
        public PostService(IPostRepository postRepository)
        {
            _IPostRepository = postRepository;
        }
        public long CreatePost(CreatePostDto createPostDto)
        {
            var result = _IPostRepository.CreatePost(createPostDto);
            return result;
        }
        public List<MstPosts> GetPosts()
        {
            var result = _IPostRepository.GetPosts();
            return result;
        }
        public List<MstPostCategories> GetPostCategories()
        {
            var result = _IPostRepository.GetPostCategories();
            return result;
        }
        public int CreateOrUpdateLikes(CreateLikeDto createLikeDto)
        {
            var result = _IPostRepository.CreateOrUpdateLikes(createLikeDto);
            return result;
        }

        public int ClosePost(int postId, int closedBy)
        {
            var result = _IPostRepository.ClosePost(postId, closedBy);
            return result;
        }

        public int DeletePost(int postId, int deletedBy)
        {
            var result = _IPostRepository.DeletePost(postId, deletedBy);
            return result;
        }

        public int CreateOrUpdateComment(CreateCommentDto createCommentDto)
        {
            var result = _IPostRepository.CreateOrUpdateComment(createCommentDto);
            return result;
        }

        public MstPosts GetPostById(int postId)
        {
            var result = _IPostRepository.GetPostById(postId);
            return result;
        }

        public List<MstPosts> GetUnresolvedPosts()
        {
            var result = _IPostRepository.GetUnresolvedPosts();
            return result;
        }

        public int CloseObservation(CloseObservationDto closeObservationDto)
        {
            var result = _IPostRepository.CloseObservation(closeObservationDto);
            return result;
        }

        public int SendDailyReminders()
        {
            var result = _IPostRepository.SendDailyReminders();
            return result;
        }
    }
}
