using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Request
{
    public class CreateFeedbackDto
    {
        public int FeedbackID { get; set; }
        public int UserID { get; set; }
        public int? FacilityID { get; set; }
        public string FeedbackType { get; set; }
        public string Subject { get; set; }
        public string Description { get; set; }
        public string Priority { get; set; }
        public string AttachmentURL { get; set; }
    }

    public class AcknowledgeAnnouncementDto
    {
        public int AnnouncementID { get; set; }
        public int UserID { get; set; }
        public bool IsAcknowledged { get; set; }
    }

    public class ReactToAnnouncementDto
    {
        public int AnnouncementID { get; set; }
        public int UserID { get; set; }
        public string ReactionType { get; set; }
    }

    public class RegisterForEventDto
    {
        public int EventID { get; set; }
        public int UserID { get; set; }
        public bool IsAccepted { get; set; }
    }

    public class CloseObservationDto
    {
        public int PostID { get; set; }
        public int ClosedBy { get; set; }
        public string RectificationComment { get; set; }
        public string AfterImageURL { get; set; }
    }

    public class InspectionReminderDto
    {
        public int InspectionID { get; set; }
        public int SentBy { get; set; }
        public List<int> ActionPartyIds { get; set; } = new List<int>();
        public string ReminderMessage { get; set; }
    }

    public class RemedyInspectionDto
    {
        public int ItemID { get; set; }
        public int ActionPartyID { get; set; }
        public string RemedyDescription { get; set; }
        public List<string> RemedyImageURLs { get; set; } = new List<string>();
        public bool IsCompleted { get; set; }
    }
}
