﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class PostController : ControllerBase
    {
        private readonly IPostService _IPostService;
        private readonly ILogger<PostController> _logger;
        public PostController(IPostService postService, IConfiguration configuration, ILogger<PostController> logger)
        {
            _logger = logger;
            _IPostService = postService;
        }
        [HttpPost("CreatePost")]
        public IActionResult CreatePost([FromBody] CreatePostDto createPostDto)
        {
            var response = new ResponseDetails();
            try
            {
                long result = _IPostService.CreatePost(createPostDto);
                response.Status = 1;
                response.Message = "Post created successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while creating post";
                response.Result = null;
                return BadRequest(response);
            }
        }
        [HttpGet("GetPosts")]
        public IActionResult GetPosts()
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IPostService.GetPosts();
                response.Status = 1;
                response.Message = "Posts retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving posts";
                response.Result = null;
                return BadRequest(response);
            }
        }
        [HttpGet("GetPostCategories")]
        public IActionResult GetPostCategories()
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IPostService.GetPostCategories();
                response.Status = 1;
                response.Message = "Post categories retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving post categories";
                response.Result = null;
                return BadRequest(response);
            }
        }
        [HttpPost("CreateOrUpdateLikes")]
        public IActionResult CreateOrUpdateLikes([FromBody] CreateLikeDto createLikeDto)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IPostService.CreateOrUpdateLikes(createLikeDto);
                response.Status = 1;
                response.Message = "Like operation completed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing like operation";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPut("ClosePost")]
        public IActionResult ClosePost(int postId, int closedBy)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IPostService.ClosePost(postId, closedBy);
                response.Status = 1;
                response.Message = "Post closed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while closing post";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpDelete("DeletePost")]
        public IActionResult DeletePost(int postId, int deletedBy)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IPostService.DeletePost(postId, deletedBy);
                response.Status = 1;
                response.Message = "Post deleted successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while deleting post";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPost("CreateOrUpdateComment")]
        public IActionResult CreateOrUpdateComment([FromBody] CreateCommentDto createCommentDto)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IPostService.CreateOrUpdateComment(createCommentDto);
                response.Status = 1;
                response.Message = "Comment operation completed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing comment operation";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpGet("{id}")]
        public IActionResult GetPostById(int id)
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IPostService.GetPostById(id);
                response.Status = 1;
                response.Message = "Post retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving post";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpGet("unresolved")]
        public IActionResult GetUnresolvedPosts()
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IPostService.GetUnresolvedPosts();
                response.Status = 1;
                response.Message = "Unresolved posts retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving unresolved posts";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPut("{id}/close-observation")]
        public IActionResult CloseObservation(int id, [FromBody] CloseObservationDto closeObservationDto)
        {
            var response = new ResponseDetails();
            try
            {
                closeObservationDto.PostID = id; // Ensure the ID matches the route
                int result = _IPostService.CloseObservation(closeObservationDto);
                response.Status = 1;
                response.Message = "Observation closed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while closing observation";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPost("daily-reminders")]
        public IActionResult SendDailyReminders()
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IPostService.SendDailyReminders();
                response.Status = 1;
                response.Message = $"Daily reminders sent successfully to {result} users";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while sending daily reminders";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPost("{id}/comments")]
        public IActionResult AddComment(int id, [FromBody] CreateCommentDto createCommentDto)
        {
            var response = new ResponseDetails();
            try
            {
                createCommentDto.PostID = id; // Ensure the PostID matches the route
                createCommentDto.CommentID = 0; // Force creation of new comment
                int result = _IPostService.CreateOrUpdateComment(createCommentDto);
                response.Status = 1;
                response.Message = "Comment added successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while adding comment";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPost("{id}/reactions")]
        public IActionResult ReactToPost(int id, [FromBody] CreateLikeDto createLikeDto)
        {
            var response = new ResponseDetails();
            try
            {
                createLikeDto.PostID = id; // Ensure the PostID matches the route
                createLikeDto.EventID = null; // This is for posts, not events
                int result = _IPostService.CreateOrUpdateLikes(createLikeDto);
                response.Status = 1;
                response.Message = "Reaction processed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing reaction";
                response.Result = null;
                return BadRequest(response);
            }
        }
    }
}
