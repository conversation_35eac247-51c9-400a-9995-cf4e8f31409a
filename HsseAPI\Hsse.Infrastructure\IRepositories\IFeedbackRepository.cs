using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IFeedbackRepository
    {
        long CreateFeedback(CreateFeedbackDto createFeedbackDto);
        List<MstFeedback> GetAllFeedback();
        List<MstFeedback> GetFeedbackByFacility(int facilityId);
        MstFeedback GetFeedbackById(int feedbackId);
        int UpdateFeedbackStatus(int feedbackId, string status, string adminResponse, int respondedBy);
    }
}
