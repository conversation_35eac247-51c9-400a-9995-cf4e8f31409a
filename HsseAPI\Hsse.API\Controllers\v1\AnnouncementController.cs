using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class AnnouncementController : ControllerBase
    {
        private readonly IAnnouncementService _IAnnouncementService;
        private readonly ILogger<AnnouncementController> _logger;
        public AnnouncementController(IAnnouncementService announcementService, IConfiguration configuration, ILogger<AnnouncementController> logger)
        {
            _logger = logger;
            _IAnnouncementService = announcementService;
        }

        [HttpPost("CreateOrUpdateAnnouncement")]
        public IActionResult CreateOrUpdateAnnouncement([FromBody] CreateAnnouncementDto createAnnouncementDto)
        {
            var response = new ResponseDetails();
            try
            {
                long result = _IAnnouncementService.CreateOrUpdateAnnouncement(createAnnouncementDto);
                response.Status = 1;
                response.Message = "Announcement operation completed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing announcement operation";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPost("CreateOrUpdateCategory")]
        public IActionResult CreateOrUpdateCategory([FromBody] CreateAnnouncementCategoryDto createCategoryDto)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IAnnouncementService.CreateOrUpdateCategory(createCategoryDto);
                response.Status = 1;
                response.Message = "Category operation completed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing category operation";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpGet("GetCategories")]
        public IActionResult GetCategories()
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IAnnouncementService.GetCategories();
                response.Status = 1;
                response.Message = "Categories retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving categories";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpGet("GetAnnouncements")]
        public IActionResult GetAnnouncements()
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IAnnouncementService.GetAnnouncements();
                response.Status = 1;
                response.Message = "Announcements retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving announcements";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPost("{id}/acknowledge")]
        public IActionResult AcknowledgeAnnouncement(int id, [FromBody] AcknowledgeAnnouncementDto acknowledgeDto)
        {
            var response = new ResponseDetails();
            try
            {
                acknowledgeDto.AnnouncementID = id; // Ensure the ID matches the route
                int result = _IAnnouncementService.AcknowledgeAnnouncement(acknowledgeDto);
                response.Status = 1;
                response.Message = "Announcement acknowledged successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while acknowledging announcement";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPost("{id}/react")]
        public IActionResult ReactToAnnouncement(int id, [FromBody] ReactToAnnouncementDto reactDto)
        {
            var response = new ResponseDetails();
            try
            {
                reactDto.AnnouncementID = id; // Ensure the ID matches the route
                int result = _IAnnouncementService.ReactToAnnouncement(reactDto);
                response.Status = 1;
                response.Message = "Reaction to announcement processed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing reaction to announcement";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPost("{id}/register")]
        public IActionResult RegisterForEvent(int id, [FromBody] RegisterForEventDto registerDto)
        {
            var response = new ResponseDetails();
            try
            {
                registerDto.EventID = id; // Ensure the ID matches the route (assuming this is for events)
                int result = _IAnnouncementService.RegisterForEvent(registerDto);
                response.Status = 1;
                response.Message = "Event registration processed successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while processing event registration";
                response.Result = null;
                return BadRequest(response);
            }
        }
    }
}
