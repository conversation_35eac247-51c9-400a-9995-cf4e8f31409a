﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class PostRepository : IPostRepository
    {
        private readonly MasterDBContext _MasterDBContext;
        public PostRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }

        public long CreatePost(CreatePostDto createPostDto)
        {
            if (createPostDto.PostID == 0)
            {
                // Create new post
                var newPost = new MstPosts
                {
                    UserID = createPostDto.UserID,
                    FacilityID = createPostDto.FacilityID,
                    Title = createPostDto.Title,
                    Description = createPostDto.Description,
                    PostType = createPostDto.PostType,
                    Location = createPostDto.Location,
                    TaggedCategoryId = createPostDto.TaggedCategoryId,
                    RequiresFollowup = createPostDto.RequiresFollowup,
                    Status = createPostDto.Status,
                    CreatedAt = DateTime.Now,
                    IsDeleted = false
                };

                _MasterDBContext.MstPosts.Add(newPost);
                _MasterDBContext.SaveChanges();

                if (!string.IsNullOrEmpty(createPostDto.MediaURL))
                {
                    var media = new MstPostMedia
                    {
                        PostID = newPost.PostID,
                        MediaURL = createPostDto.MediaURL,
                        CreatedAt = DateTime.Now
                    };

                    _MasterDBContext.MstPostMedia.Add(media);
                    _MasterDBContext.SaveChanges();
                }

                return newPost.PostID;
            }
            else
            {
                // Update existing post
                var existingPost = _MasterDBContext.MstPosts
                    .FirstOrDefault(p => p.PostID == createPostDto.PostID && !p.IsDeleted);

                if (existingPost == null)
                    return 0;

                existingPost.UserID = createPostDto.UserID;
                existingPost.FacilityID = createPostDto.FacilityID;
                existingPost.Title = createPostDto.Title;
                existingPost.Description = createPostDto.Description;
                existingPost.PostType = createPostDto.PostType;
                existingPost.Location = createPostDto.Location;
                existingPost.TaggedCategoryId = createPostDto.TaggedCategoryId;
                existingPost.RequiresFollowup = createPostDto.RequiresFollowup;
                existingPost.Status = createPostDto.Status;
                existingPost.UpdatedAt = DateTime.Now;

                var existingMedia = _MasterDBContext.MstPostMedia
                    .FirstOrDefault(m => m.PostID == createPostDto.PostID);

                if (!string.IsNullOrEmpty(createPostDto.MediaURL))
                {
                    if (existingMedia != null)
                    {
                        existingMedia.MediaURL = createPostDto.MediaURL;
                    }
                    else
                    {
                        var newMedia = new MstPostMedia
                        {
                            PostID = createPostDto.PostID,
                            MediaURL = createPostDto.MediaURL,
                            CreatedAt = DateTime.Now
                        };
                        _MasterDBContext.MstPostMedia.Add(newMedia);
                    }
                }

                _MasterDBContext.SaveChanges();
                return existingPost.PostID;
            }
        }
        public List<MstPosts> GetPosts()
        {
           return _MasterDBContext.MstPosts.ToList();
        }
        public List<MstPostCategories> GetPostCategories()
        {
            return _MasterDBContext.MstPostCategories.ToList();
        }
        public int CreateOrUpdateLikes(CreateLikeDto createLikeDto)
        {
            // Ensure exactly one of PostID or EventID is provided
            if ((createLikeDto.PostID == null && createLikeDto.EventID == null) ||
                (createLikeDto.PostID != null && createLikeDto.EventID != null))
            {
                throw new ArgumentException("Like must be for either a Post or an Event, not both.");
            }

            // Query only for one of the non-null keys
            var result = _MasterDBContext.MstLikesConfig.FirstOrDefault(x =>
                x.UserID == createLikeDto.UserID &&
                ((createLikeDto.PostID != null && x.PostID == createLikeDto.PostID) ||
                 (createLikeDto.EventID != null && x.EventID == createLikeDto.EventID)));

            if (result == null)
            {
                var newLike = new MstLikesConfig
                {
                    UserID = createLikeDto.UserID,
                    PostID = createLikeDto.PostID,
                    EventID = createLikeDto.EventID,
                    IsLiked = createLikeDto.IsLiked,
                    LikedAt = DateTime.Now
                };

                _MasterDBContext.MstLikesConfig.Add(newLike);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }

            // Update existing
            result.IsLiked = createLikeDto.IsLiked;
            result.LikedAt = DateTime.Now;

            _MasterDBContext.SaveChanges();
            return 2; // Updated
        }

        public int ClosePost(int postId, int closedBy)
        {
            var post = _MasterDBContext.MstPosts
                .FirstOrDefault(p => p.PostID == postId && !p.IsDeleted);

            if (post == null)
                return 0; // Post not found

            post.ClosedBy = closedBy;
            post.UpdatedAt = DateTime.Now;
            // Assuming status 2 means closed - adjust based on your business logic
            post.Status = 2;

            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public int DeletePost(int postId, int deletedBy)
        {
            var post = _MasterDBContext.MstPosts
                .FirstOrDefault(p => p.PostID == postId && !p.IsDeleted);

            if (post == null)
                return 0; // Post not found

            post.DeletedBy = deletedBy;
            post.IsDeleted = true;
            post.UpdatedAt = DateTime.Now;

            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public int CreateOrUpdateComment(CreateCommentDto createCommentDto)
        {
            if (createCommentDto.CommentID == 0)
            {
                // Create new comment
                var newComment = new MstPostComments
                {
                    PostID = createCommentDto.PostID,
                    UserID = createCommentDto.UserID,
                    CommentText = createCommentDto.CommentText,
                    CommentedAt = DateTime.Now
                };

                _MasterDBContext.MstPostComments.Add(newComment);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }
            else
            {
                // Update existing comment
                var existingComment = _MasterDBContext.MstPostComments
                    .FirstOrDefault(c => c.CommentID == createCommentDto.CommentID);

                if (existingComment == null)
                    return 0; // Comment not found

                existingComment.CommentText = createCommentDto.CommentText;
                existingComment.CommentedAt = DateTime.Now;

                _MasterDBContext.SaveChanges();
                return 2; // Updated
            }
        }

        public MstPosts GetPostById(int postId)
        {
            return _MasterDBContext.MstPosts
                .FirstOrDefault(p => p.PostID == postId && !p.IsDeleted);
        }

        public List<MstPosts> GetUnresolvedPosts()
        {
            // Assuming status 1 = open/unresolved, adjust based on your business logic
            return _MasterDBContext.MstPosts
                .Where(p => !p.IsDeleted && (p.Status == 1 || p.Status == null))
                .OrderByDescending(p => p.CreatedAt)
                .ToList();
        }

        public int CloseObservation(CloseObservationDto closeObservationDto)
        {
            var post = _MasterDBContext.MstPosts
                .FirstOrDefault(p => p.PostID == closeObservationDto.PostID && !p.IsDeleted);

            if (post == null)
                return 0; // Post not found

            post.ClosedBy = closeObservationDto.ClosedBy;
            post.UpdatedAt = DateTime.Now;
            post.Status = 2; // Closed status

            // Update media with after image
            var media = _MasterDBContext.MstPostMedia
                .FirstOrDefault(m => m.PostID == closeObservationDto.PostID);

            if (media != null && !string.IsNullOrEmpty(closeObservationDto.AfterImageURL))
            {
                media.AfterMediaURL = closeObservationDto.AfterImageURL;
            }

            // Add follow-up record with rectification comment
            var followup = new MstFollowupPosts
            {
                PostID = closeObservationDto.PostID,
                CreatedBy = closeObservationDto.ClosedBy,
                CreatedDate = DateTime.Now,
                CompletedBy = closeObservationDto.ClosedBy,
                CompletedTime = DateTime.Now,
                Comments = closeObservationDto.RectificationComment,
                Format = "Observation Closure"
            };

            _MasterDBContext.MstFollowupPosts.Add(followup);
            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public int SendDailyReminders()
        {
            // Get all unresolved posts older than 24 hours
            var unresolvedPosts = _MasterDBContext.MstPosts
                .Where(p => !p.IsDeleted &&
                           (p.Status == 1 || p.Status == null) &&
                           p.CreatedAt < DateTime.Now.AddDays(-1))
                .ToList();

            // Create notifications for each unresolved post
            foreach (var post in unresolvedPosts)
            {
                var notification = new MstNotifications
                {
                    UserID = post.UserID,
                    Heading = "Unresolved Post Reminder",
                    Message = $"Your post '{post.Title}' is still unresolved. Please take action.",
                    IsRead = false,
                    CreatedAt = DateTime.Now
                };

                _MasterDBContext.MstNotifications.Add(notification);
            }

            _MasterDBContext.SaveChanges();
            return unresolvedPosts.Count; // Return number of reminders sent
        }
    }
}
