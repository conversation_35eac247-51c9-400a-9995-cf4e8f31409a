2025-06-02 09:33:33.615 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 09:33:34.135 +05:30 [INF] Now listening on: https://localhost:7231
2025-06-02 09:33:34.171 +05:30 [INF] Now listening on: http://localhost:5133
2025-06-02 09:33:34.371 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 09:33:34.401 +05:30 [INF] Hosting environment: Development
2025-06-02 09:33:34.403 +05:30 [INF] Content root path: D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API
2025-06-02 09:33:38.110 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 09:33:38.656 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 573.5344ms
2025-06-02 09:33:38.697 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 09:33:38.697 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 09:33:38.709 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 15.6519ms
2025-06-02 09:33:38.807 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 109.7338ms
2025-06-02 09:33:39.439 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 09:33:39.476 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 41.2152ms
2025-06-02 09:35:56.411 +05:30 [INF] Request starting HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateAnnouncement - application/json 465
2025-06-02 09:35:58.245 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API)'
2025-06-02 09:35:58.286 +05:30 [INF] Route matched with {action = "CreateOrUpdateAnnouncement", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult CreateOrUpdateAnnouncement(Hsse.Data.Dto.Request.CreateAnnouncementDto) on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 09:36:00.547 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API) in 2253.7818ms
2025-06-02 09:36:00.550 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API)'
2025-06-02 09:36:00.556 +05:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The entity type 'MstRoleMenuPermission' requires a primary key to be defined. If you intended to use a keyless entity type, call 'HasNoKey' in 'OnModelCreating'. For more information on keyless entity types, see https://go.microsoft.com/fwlink/?linkid=2141943.
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.ValidateNonNullPrimaryKeys(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.RelationalModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelRuntimeInitializer.Initialize(IModel model, Boolean designTime, IDiagnosticsLogger`1 validationLogger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_InternalServiceProvider()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies.get_StateManager()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.EntryWithoutDetectChanges(TEntity entity)
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.Add(TEntity entity)
   at Hsse.Infrastructure.Repositories.AnnouncementRepository.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\AnnouncementRepository.cs:line 41
   at Hsse.Application.Services.AnnouncementService.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.Application\Services\AnnouncementService.cs:line 24
   at Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API\Controllers\v1\AnnouncementController.cs:line 24
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 09:36:00.687 +05:30 [INF] Request finished HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateAnnouncement - 500 null text/plain; charset=utf-8 4277.0045ms
2025-06-02 09:38:48.600 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 09:38:49.226 +05:30 [INF] Now listening on: https://localhost:7231
2025-06-02 09:38:49.229 +05:30 [INF] Now listening on: http://localhost:5133
2025-06-02 09:38:49.422 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 09:38:49.450 +05:30 [INF] Hosting environment: Development
2025-06-02 09:38:49.453 +05:30 [INF] Content root path: D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API
2025-06-02 09:38:51.033 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 09:38:51.622 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 603.5452ms
2025-06-02 09:38:51.647 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 09:38:51.648 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 09:38:51.659 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 12.6057ms
2025-06-02 09:38:51.742 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 93.9114ms
2025-06-02 09:38:52.381 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 09:38:52.437 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 59.0309ms
2025-06-02 09:40:06.636 +05:30 [INF] Request starting HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateAnnouncement - application/json 465
2025-06-02 09:40:06.754 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API)'
2025-06-02 09:40:06.803 +05:30 [INF] Route matched with {action = "CreateOrUpdateAnnouncement", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult CreateOrUpdateAnnouncement(Hsse.Data.Dto.Request.CreateAnnouncementDto) on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 09:40:10.441 +05:30 [ERR] Failed executing DbCommand (176ms) [Parameters=[@p0='?' (Size = 4000), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime2), @p10='?' (DbType = Int32), @p11='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [MstAnnouncements] ([AnnouncementDocument], [CategoryId], [CreatedAt], [CreatedBy], [Description], [ExpiryAt], [FacilityID], [ModifiedAt], [ModifiedBy], [ScheduleAt], [Status], [Title])
OUTPUT INSERTED.[AnnouncementsId]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11);
2025-06-02 09:40:10.484 +05:30 [ERR] An exception occurred in the database while saving changes for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
Invalid column name 'AnnouncementsId'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
ClientConnectionId:8d0015c6-e7c3-445b-88b7-a07cf3b917ac
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
Invalid column name 'AnnouncementsId'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
ClientConnectionId:8d0015c6-e7c3-445b-88b7-a07cf3b917ac
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
2025-06-02 09:40:10.509 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API) in 3695.2572ms
2025-06-02 09:40:10.512 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API)'
2025-06-02 09:40:10.517 +05:30 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
Invalid column name 'AnnouncementsId'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
ClientConnectionId:8d0015c6-e7c3-445b-88b7-a07cf3b917ac
Error Number:207,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges()
   at Hsse.Infrastructure.Repositories.AnnouncementRepository.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\AnnouncementRepository.cs:line 42
   at Hsse.Application.Services.AnnouncementService.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.Application\Services\AnnouncementService.cs:line 24
   at Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API\Controllers\v1\AnnouncementController.cs:line 24
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 09:40:10.590 +05:30 [INF] Request finished HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateAnnouncement - 500 null text/plain; charset=utf-8 3953.8003ms
2025-06-02 09:43:31.405 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 09:43:31.832 +05:30 [INF] Now listening on: https://localhost:7231
2025-06-02 09:43:31.835 +05:30 [INF] Now listening on: http://localhost:5133
2025-06-02 09:43:32.037 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 09:43:32.043 +05:30 [INF] Hosting environment: Development
2025-06-02 09:43:32.058 +05:30 [INF] Content root path: D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API
2025-06-02 09:43:33.536 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 09:43:34.258 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 732.0244ms
2025-06-02 09:43:34.310 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 09:43:34.310 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 09:43:34.331 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 20.7086ms
2025-06-02 09:43:34.373 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 63.3706ms
2025-06-02 09:43:35.021 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 09:43:35.113 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 92.6636ms
2025-06-02 09:43:41.109 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Announcement/GetCategories - null null
2025-06-02 09:43:42.557 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.GetCategories (Hsse.API)'
2025-06-02 09:43:42.582 +05:30 [INF] Route matched with {action = "GetCategories", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetCategories() on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 09:43:46.304 +05:30 [INF] Executed DbCommand (95ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[AnnoucementCategoryId], [m].[AnnoucementCategoryName], [m].[CreatedAt], [m].[CreatedBy], [m].[ModifiedAt], [m].[ModifiedBy], [m].[Status]
FROM [MstAnnouncementCategory] AS [m]
2025-06-02 09:43:46.448 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Hsse.Data.Entities.MstAnnouncementCategory, Hsse.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 09:43:46.511 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.GetCategories (Hsse.API) in 3917.8573ms
2025-06-02 09:43:46.525 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.GetCategories (Hsse.API)'
2025-06-02 09:43:46.542 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Announcement/GetCategories - 200 null application/json; charset=utf-8 5433.0551ms
2025-06-02 09:43:55.301 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Announcement/GetAnnouncements - null null
2025-06-02 09:43:55.342 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.GetAnnouncements (Hsse.API)'
2025-06-02 09:43:55.346 +05:30 [INF] Route matched with {action = "GetAnnouncements", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAnnouncements() on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 09:43:55.450 +05:30 [ERR] Failed executing DbCommand (68ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[AnnouncementsId], [m].[AnnouncementDocument], [m].[CategoryId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[ExpiryAt], [m].[FacilityID], [m].[ModifiedAt], [m].[ModifiedBy], [m].[ScheduleAt], [m].[Status], [m].[Title]
FROM [MstAnnouncements] AS [m]
2025-06-02 09:43:55.484 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'AnnouncementsId'.
Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:59b70e53-e4ad-42ee-bec4-b627a587fcba
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'AnnouncementsId'.
Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:59b70e53-e4ad-42ee-bec4-b627a587fcba
Error Number:207,State:1,Class:16
2025-06-02 09:43:55.497 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.GetAnnouncements (Hsse.API) in 146.9456ms
2025-06-02 09:43:55.501 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.GetAnnouncements (Hsse.API)'
2025-06-02 09:43:55.511 +05:30 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'AnnouncementsId'.
Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Hsse.Infrastructure.Repositories.AnnouncementRepository.GetAnnouncements() in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\AnnouncementRepository.cs:line 211
   at Hsse.Application.Services.AnnouncementService.GetAnnouncements() in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.Application\Services\AnnouncementService.cs:line 48
   at lambda_method85(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:59b70e53-e4ad-42ee-bec4-b627a587fcba
Error Number:207,State:1,Class:16
2025-06-02 09:43:55.571 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Announcement/GetAnnouncements - 500 null text/plain; charset=utf-8 269.7212ms
2025-06-02 09:50:46.611 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 09:50:46.953 +05:30 [INF] Now listening on: https://localhost:7231
2025-06-02 09:50:46.956 +05:30 [INF] Now listening on: http://localhost:5133
2025-06-02 09:50:47.103 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 09:50:47.147 +05:30 [INF] Hosting environment: Development
2025-06-02 09:50:47.148 +05:30 [INF] Content root path: D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API
2025-06-02 09:50:49.271 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 09:50:49.824 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 569.3619ms
2025-06-02 09:50:49.887 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 09:50:49.889 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 09:50:49.938 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 50.6684ms
2025-06-02 09:50:50.049 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 160.1399ms
2025-06-02 09:50:50.708 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 09:50:50.765 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 57.0501ms
2025-06-02 09:51:33.498 +05:30 [INF] Request starting HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateAnnouncement - application/json 465
2025-06-02 09:51:35.230 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API)'
2025-06-02 09:51:35.279 +05:30 [INF] Route matched with {action = "CreateOrUpdateAnnouncement", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult CreateOrUpdateAnnouncement(Hsse.Data.Dto.Request.CreateAnnouncementDto) on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 09:51:38.712 +05:30 [INF] Executed DbCommand (194ms) [Parameters=[@p0='?' (Size = 4000), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime2), @p10='?' (DbType = Int32), @p11='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [MstAnnouncements] ([AnnouncementDocument], [CategoryId], [Created_at], [Created_by], [Description], [ExpiryAt], [FacilityID], [Modified_at], [Modified_by], [ScheduleAt], [Status], [Title])
OUTPUT INSERTED.[Announcements_id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11);
2025-06-02 09:51:38.969 +05:30 [INF] Executed DbCommand (60ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 4000), @p2='?' (Size = 4000), @p3='?' (DbType = Int32), @p4='?' (DbType = Boolean), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Boolean), @p10='?' (DbType = Int32), @p11='?' (DbType = Int32), @p12='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [MstAnnouncementDocuments] ([AnnouncementID], [DocumentFile], [DocumentName])
OUTPUT INSERTED.[DocumentID]
VALUES (@p0, @p1, @p2);
MERGE [MstAnnouncementReceivers] USING (
VALUES (@p3, @p4, @p5, @p6, @p7, 0),
(@p8, @p9, @p10, @p11, @p12, 1)) AS i ([AnnouncementID], [Delivered], [EventID], [GroupID], [UserID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AnnouncementID], [Delivered], [EventID], [GroupID], [UserID])
VALUES (i.[AnnouncementID], i.[Delivered], i.[EventID], i.[GroupID], i.[UserID])
OUTPUT INSERTED.[ReceiverID], i._Position;
2025-06-02 09:51:39.073 +05:30 [ERR] An exception occurred in the database while saving changes for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): String or binary data would be truncated in table 'HSSE_DB_Latest.dbo.mstAnnouncementDocuments', column 'DocumentFile'. Truncated value: 't'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:8c738b57-c9ba-497f-966a-a4b15f098b95
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): String or binary data would be truncated in table 'HSSE_DB_Latest.dbo.mstAnnouncementDocuments', column 'DocumentFile'. Truncated value: 't'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:8c738b57-c9ba-497f-966a-a4b15f098b95
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
2025-06-02 09:51:39.092 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API) in 3803.6875ms
2025-06-02 09:51:39.095 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API)'
2025-06-02 09:51:39.103 +05:30 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): String or binary data would be truncated in table 'HSSE_DB_Latest.dbo.mstAnnouncementDocuments', column 'DocumentFile'. Truncated value: 't'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:8c738b57-c9ba-497f-966a-a4b15f098b95
Error Number:2628,State:1,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges()
   at Hsse.Infrastructure.Repositories.AnnouncementRepository.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\AnnouncementRepository.cs:line 79
   at Hsse.Application.Services.AnnouncementService.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.Application\Services\AnnouncementService.cs:line 24
   at Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API\Controllers\v1\AnnouncementController.cs:line 24
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 09:51:39.178 +05:30 [INF] Request finished HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateAnnouncement - 500 null text/plain; charset=utf-8 5679.5248ms
2025-06-02 09:54:01.488 +05:30 [INF] Request starting HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateAnnouncement - application/json 459
2025-06-02 09:54:01.508 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API)'
2025-06-02 09:54:01.511 +05:30 [INF] Route matched with {action = "CreateOrUpdateAnnouncement", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult CreateOrUpdateAnnouncement(Hsse.Data.Dto.Request.CreateAnnouncementDto) on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 09:54:01.637 +05:30 [INF] Executed DbCommand (63ms) [Parameters=[@p0='?' (Size = 4000), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime2), @p10='?' (DbType = Int32), @p11='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [MstAnnouncements] ([AnnouncementDocument], [CategoryId], [Created_at], [Created_by], [Description], [ExpiryAt], [FacilityID], [Modified_at], [Modified_by], [ScheduleAt], [Status], [Title])
OUTPUT INSERTED.[Announcements_id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11);
2025-06-02 09:54:01.779 +05:30 [INF] Executed DbCommand (63ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 4000), @p2='?' (Size = 4000), @p3='?' (DbType = Int32), @p4='?' (DbType = Boolean), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Boolean), @p10='?' (DbType = Int32), @p11='?' (DbType = Int32), @p12='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [MstAnnouncementDocuments] ([AnnouncementID], [DocumentFile], [DocumentName])
OUTPUT INSERTED.[DocumentID]
VALUES (@p0, @p1, @p2);
MERGE [MstAnnouncementReceivers] USING (
VALUES (@p3, @p4, @p5, @p6, @p7, 0),
(@p8, @p9, @p10, @p11, @p12, 1)) AS i ([AnnouncementID], [Delivered], [EventID], [GroupID], [UserID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AnnouncementID], [Delivered], [EventID], [GroupID], [UserID])
VALUES (i.[AnnouncementID], i.[Delivered], i.[EventID], i.[GroupID], i.[UserID])
OUTPUT INSERTED.[ReceiverID], i._Position;
2025-06-02 09:54:01.841 +05:30 [ERR] An exception occurred in the database while saving changes for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The MERGE statement conflicted with the FOREIGN KEY constraint "FK__mstAnnoun__UserI__03F0984C". The conflict occurred in database "HSSE_DB_Latest", table "dbo.mstUsers", column 'UserID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:8c738b57-c9ba-497f-966a-a4b15f098b95
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The MERGE statement conflicted with the FOREIGN KEY constraint "FK__mstAnnoun__UserI__03F0984C". The conflict occurred in database "HSSE_DB_Latest", table "dbo.mstUsers", column 'UserID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:8c738b57-c9ba-497f-966a-a4b15f098b95
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
2025-06-02 09:54:01.856 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API) in 343.6737ms
2025-06-02 09:54:01.858 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API)'
2025-06-02 09:54:01.860 +05:30 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The MERGE statement conflicted with the FOREIGN KEY constraint "FK__mstAnnoun__UserI__03F0984C". The conflict occurred in database "HSSE_DB_Latest", table "dbo.mstUsers", column 'UserID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:8c738b57-c9ba-497f-966a-a4b15f098b95
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges()
   at Hsse.Infrastructure.Repositories.AnnouncementRepository.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\AnnouncementRepository.cs:line 79
   at Hsse.Application.Services.AnnouncementService.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.Application\Services\AnnouncementService.cs:line 24
   at Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto) in D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API\Controllers\v1\AnnouncementController.cs:line 24
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 09:54:01.874 +05:30 [INF] Request finished HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateAnnouncement - 500 null text/plain; charset=utf-8 386.6826ms
2025-06-02 09:56:01.563 +05:30 [INF] Request starting HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateAnnouncement - application/json 459
2025-06-02 09:56:01.571 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API)'
2025-06-02 09:56:01.572 +05:30 [INF] Route matched with {action = "CreateOrUpdateAnnouncement", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult CreateOrUpdateAnnouncement(Hsse.Data.Dto.Request.CreateAnnouncementDto) on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 09:56:01.662 +05:30 [INF] Executed DbCommand (54ms) [Parameters=[@p0='?' (Size = 4000), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (Size = 4000), @p5='?' (DbType = DateTime2), @p6='?' (DbType = Int32), @p7='?' (DbType = DateTime2), @p8='?' (DbType = Int32), @p9='?' (DbType = DateTime2), @p10='?' (DbType = Int32), @p11='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [MstAnnouncements] ([AnnouncementDocument], [CategoryId], [Created_at], [Created_by], [Description], [ExpiryAt], [FacilityID], [Modified_at], [Modified_by], [ScheduleAt], [Status], [Title])
OUTPUT INSERTED.[Announcements_id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11);
2025-06-02 09:56:01.760 +05:30 [INF] Executed DbCommand (47ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (Size = 4000), @p2='?' (Size = 4000), @p3='?' (DbType = Int32), @p4='?' (DbType = Boolean), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Boolean), @p10='?' (DbType = Int32), @p11='?' (DbType = Int32), @p12='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [MstAnnouncementDocuments] ([AnnouncementID], [DocumentFile], [DocumentName])
OUTPUT INSERTED.[DocumentID]
VALUES (@p0, @p1, @p2);
MERGE [MstAnnouncementReceivers] USING (
VALUES (@p3, @p4, @p5, @p6, @p7, 0),
(@p8, @p9, @p10, @p11, @p12, 1)) AS i ([AnnouncementID], [Delivered], [EventID], [GroupID], [UserID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AnnouncementID], [Delivered], [EventID], [GroupID], [UserID])
VALUES (i.[AnnouncementID], i.[Delivered], i.[EventID], i.[GroupID], i.[UserID])
OUTPUT INSERTED.[ReceiverID], i._Position;
2025-06-02 09:56:01.854 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Int64'.
2025-06-02 09:56:01.872 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API) in 296.173ms
2025-06-02 09:56:01.883 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateAnnouncement (Hsse.API)'
2025-06-02 09:56:01.885 +05:30 [INF] Request finished HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateAnnouncement - 200 null application/json; charset=utf-8 322.136ms
2025-06-02 10:03:31.779 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 10:03:32.306 +05:30 [INF] Now listening on: https://localhost:7231
2025-06-02 10:03:32.309 +05:30 [INF] Now listening on: http://localhost:5133
2025-06-02 10:03:32.408 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 10:03:32.410 +05:30 [INF] Hosting environment: Development
2025-06-02 10:03:32.412 +05:30 [INF] Content root path: D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API
2025-06-02 10:03:33.071 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 10:03:33.429 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 10:03:33.447 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 10:03:33.447 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 357.6714ms
2025-06-02 10:03:33.461 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 31.8979ms
2025-06-02 10:03:33.532 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 84.5415ms
2025-06-02 10:03:34.158 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 10:03:34.203 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 44.7175ms
2025-06-02 10:03:46.838 +05:30 [INF] Request starting HTTP/2 DELETE https://localhost:7231/api/v1/Announcement/DeleteAnnouncement?announcementId=38&deletedBy=7 - null null
2025-06-02 10:03:48.577 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.DeleteAnnouncement (Hsse.API)'
2025-06-02 10:03:48.628 +05:30 [INF] Route matched with {action = "DeleteAnnouncement", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult DeleteAnnouncement(Int32, Int32) on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 10:03:58.744 +05:30 [INF] Executed DbCommand (159ms) [Parameters=[@__announcementId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [m].[Announcements_id], [m].[AnnouncementDocument], [m].[CategoryId], [m].[Created_at], [m].[Created_by], [m].[Description], [m].[ExpiryAt], [m].[FacilityID], [m].[Modified_at], [m].[Modified_by], [m].[ScheduleAt], [m].[Status], [m].[Title]
FROM [MstAnnouncements] AS [m]
WHERE [m].[Announcements_id] = @__announcementId_0
2025-06-02 10:04:14.008 +05:30 [INF] Executed DbCommand (55ms) [Parameters=[@p3='?' (DbType = Int32), @p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [MstAnnouncements] SET [Modified_at] = @p0, [Modified_by] = @p1, [Status] = @p2
OUTPUT 1
WHERE [Announcements_id] = @p3;
2025-06-02 10:04:20.271 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Int32'.
2025-06-02 10:04:20.283 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.DeleteAnnouncement (Hsse.API) in 31648.4368ms
2025-06-02 10:04:20.286 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.DeleteAnnouncement (Hsse.API)'
2025-06-02 10:04:20.297 +05:30 [INF] Request finished HTTP/2 DELETE https://localhost:7231/api/v1/Announcement/DeleteAnnouncement?announcementId=38&deletedBy=7 - 200 null application/json; charset=utf-8 33459.0381ms
2025-06-02 10:04:33.096 +05:30 [INF] Request starting HTTP/2 DELETE https://localhost:7231/api/v1/Announcement/DeleteAnnouncement?announcementId=38&deletedBy=7 - null null
2025-06-02 10:04:33.126 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.DeleteAnnouncement (Hsse.API)'
2025-06-02 10:04:33.128 +05:30 [INF] Route matched with {action = "DeleteAnnouncement", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult DeleteAnnouncement(Int32, Int32) on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 10:04:42.627 +05:30 [INF] Executed DbCommand (53ms) [Parameters=[@__announcementId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [m].[Announcements_id], [m].[AnnouncementDocument], [m].[CategoryId], [m].[Created_at], [m].[Created_by], [m].[Description], [m].[ExpiryAt], [m].[FacilityID], [m].[Modified_at], [m].[Modified_by], [m].[ScheduleAt], [m].[Status], [m].[Title]
FROM [MstAnnouncements] AS [m]
WHERE [m].[Announcements_id] = @__announcementId_0
2025-06-02 10:21:30.779 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 10:21:31.158 +05:30 [INF] Now listening on: https://localhost:7231
2025-06-02 10:21:31.162 +05:30 [INF] Now listening on: http://localhost:5133
2025-06-02 10:21:31.578 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 10:21:31.580 +05:30 [INF] Hosting environment: Development
2025-06-02 10:21:31.581 +05:30 [INF] Content root path: D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API
2025-06-02 10:21:32.948 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 10:21:33.496 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 560.1638ms
2025-06-02 10:21:33.517 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 10:21:33.520 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 10:21:33.548 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 30.9894ms
2025-06-02 10:21:33.651 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 131.8583ms
2025-06-02 10:21:34.272 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 10:21:34.359 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 87.1416ms
2025-06-02 10:23:26.251 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Announcement/GetCategories - null null
2025-06-02 10:23:26.361 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.GetCategories (Hsse.API)'
2025-06-02 10:23:26.398 +05:30 [INF] Route matched with {action = "GetCategories", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetCategories() on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 10:23:28.568 +05:30 [INF] Executed DbCommand (111ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[AnnoucementCategoryId], [m].[AnnoucementCategoryName], [m].[CreatedAt], [m].[CreatedBy], [m].[ModifiedAt], [m].[ModifiedBy], [m].[Status]
FROM [MstAnnouncementCategory] AS [m]
2025-06-02 10:23:28.746 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:23:28.788 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.GetCategories (Hsse.API) in 2381.1656ms
2025-06-02 10:23:28.791 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.GetCategories (Hsse.API)'
2025-06-02 10:23:28.805 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Announcement/GetCategories - 200 null application/json; charset=utf-8 2554.7004ms
2025-06-02 10:24:11.838 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Announcement/GetAnnouncements - null null
2025-06-02 10:24:11.860 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.GetAnnouncements (Hsse.API)'
2025-06-02 10:24:11.868 +05:30 [INF] Route matched with {action = "GetAnnouncements", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAnnouncements() on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 10:24:11.984 +05:30 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[Announcements_id], [m].[AnnouncementDocument], [m].[CategoryId], [m].[Created_at], [m].[Created_by], [m].[Description], [m].[ExpiryAt], [m].[FacilityID], [m].[Modified_at], [m].[Modified_by], [m].[ScheduleAt], [m].[Status], [m].[Title]
FROM [MstAnnouncements] AS [m]
2025-06-02 10:24:12.055 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:24:12.074 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.GetAnnouncements (Hsse.API) in 202.1619ms
2025-06-02 10:24:12.078 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.GetAnnouncements (Hsse.API)'
2025-06-02 10:24:12.082 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Announcement/GetAnnouncements - 200 null application/json; charset=utf-8 243.318ms
2025-06-02 10:27:06.912 +05:30 [INF] Request starting HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateCategory - application/json 127
2025-06-02 10:27:06.933 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateCategory (Hsse.API)'
2025-06-02 10:27:06.947 +05:30 [INF] Route matched with {action = "CreateOrUpdateCategory", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult CreateOrUpdateCategory(Hsse.Data.Dto.Request.CreateAnnouncementCategoryDto) on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 10:27:07.321 +05:30 [INF] Executed DbCommand (75ms) [Parameters=[@__createCategoryDto_AnnoucementCategoryId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [m].[AnnoucementCategoryId], [m].[AnnoucementCategoryName], [m].[CreatedAt], [m].[CreatedBy], [m].[ModifiedAt], [m].[ModifiedBy], [m].[Status]
FROM [MstAnnouncementCategory] AS [m]
WHERE [m].[AnnoucementCategoryId] = @__createCategoryDto_AnnoucementCategoryId_0
2025-06-02 10:27:07.533 +05:30 [INF] Executed DbCommand (52ms) [Parameters=[@p4='?' (DbType = Int32), @p0='?' (Size = 4000), @p1='?' (DbType = DateTime2), @p2='?' (DbType = Int32), @p3='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [MstAnnouncementCategory] SET [AnnoucementCategoryName] = @p0, [ModifiedAt] = @p1, [ModifiedBy] = @p2, [Status] = @p3
OUTPUT 1
WHERE [AnnoucementCategoryId] = @p4;
2025-06-02 10:27:07.548 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:27:07.560 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateCategory (Hsse.API) in 610.7156ms
2025-06-02 10:27:07.575 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.CreateOrUpdateCategory (Hsse.API)'
2025-06-02 10:27:07.578 +05:30 [INF] Request finished HTTP/2 POST https://localhost:7231/api/v1/Announcement/CreateOrUpdateCategory - 200 null application/json; charset=utf-8 665.9924ms
2025-06-02 10:27:17.602 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Announcement/GetCategories - null null
2025-06-02 10:27:17.612 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.AnnouncementController.GetCategories (Hsse.API)'
2025-06-02 10:27:17.614 +05:30 [INF] Route matched with {action = "GetCategories", controller = "Announcement"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetCategories() on controller Hsse.API.Controllers.v1.AnnouncementController (Hsse.API).
2025-06-02 10:27:17.671 +05:30 [INF] Executed DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[AnnoucementCategoryId], [m].[AnnoucementCategoryName], [m].[CreatedAt], [m].[CreatedBy], [m].[ModifiedAt], [m].[ModifiedBy], [m].[Status]
FROM [MstAnnouncementCategory] AS [m]
2025-06-02 10:27:17.675 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:27:17.678 +05:30 [INF] Executed action Hsse.API.Controllers.v1.AnnouncementController.GetCategories (Hsse.API) in 55.9065ms
2025-06-02 10:27:17.679 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.AnnouncementController.GetCategories (Hsse.API)'
2025-06-02 10:27:17.681 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Announcement/GetCategories - 200 null application/json; charset=utf-8 80.0359ms
2025-06-02 10:28:56.779 +05:30 [INF] Request starting HTTP/2 POST https://localhost:7231/api/v1/Event/CreateOrUpdateEvent - application/json 419
2025-06-02 10:28:56.838 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API)'
2025-06-02 10:28:56.841 +05:30 [INF] Route matched with {action = "CreateOrUpdateEvent", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult CreateOrUpdateEvent(Hsse.Data.Dto.Request.CreateEventDto) on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:28:57.337 +05:30 [INF] Executed DbCommand (52ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = DateTime2), @p4='?' (DbType = DateTime2), @p5='?' (Size = 4000), @p6='?' (DbType = Int32), @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?' (Size = 4000), @p10='?' (Size = 4000), @p11='?' (DbType = DateTime2), @p12='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [MstEvents] ([CreatedAt], [CreatedBy], [Description], [EventDateTime], [ExpiryAt], [ExternalLink], [FacilityID], [IsActive], [IsRsvp], [Location], [MediaURL], [ScheduleAt], [Title])
OUTPUT INSERTED.[EventID]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12);
2025-06-02 10:28:57.373 +05:30 [ERR] An exception occurred in the database while saving changes for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK_FacilityID_123495559212". The conflict occurred in database "HSSE_DB_Latest", table "dbo.mstFacilities", column 'FacilityID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:5343c763-4c57-48d5-a5ef-ef601e918f57
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK_FacilityID_123495559212". The conflict occurred in database "HSSE_DB_Latest", table "dbo.mstFacilities", column 'FacilityID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:5343c763-4c57-48d5-a5ef-ef601e918f57
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
2025-06-02 10:28:57.582 +05:30 [ERR] Method Name : CreateOrUpdateEvent, Error : An error occurred while saving the entity changes. See the inner exception for details.
2025-06-02 10:28:57.585 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:28:57.588 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API) in 744.6976ms
2025-06-02 10:28:57.590 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API)'
2025-06-02 10:28:57.592 +05:30 [INF] Request finished HTTP/2 POST https://localhost:7231/api/v1/Event/CreateOrUpdateEvent - 400 null application/json; charset=utf-8 813.6833ms
2025-06-02 10:30:45.993 +05:30 [INF] Request starting HTTP/2 POST https://localhost:7231/api/v1/Event/CreateOrUpdateEvent - application/json 419
2025-06-02 10:30:46.118 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API)'
2025-06-02 10:30:46.163 +05:30 [INF] Route matched with {action = "CreateOrUpdateEvent", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult CreateOrUpdateEvent(Hsse.Data.Dto.Request.CreateEventDto) on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:31:02.619 +05:30 [INF] Executed DbCommand (49ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = DateTime2), @p4='?' (DbType = DateTime2), @p5='?' (Size = 4000), @p6='?' (DbType = Int32), @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?' (Size = 4000), @p10='?' (Size = 4000), @p11='?' (DbType = DateTime2), @p12='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [MstEvents] ([CreatedAt], [CreatedBy], [Description], [EventDateTime], [ExpiryAt], [ExternalLink], [FacilityID], [IsActive], [IsRsvp], [Location], [MediaURL], [ScheduleAt], [Title])
OUTPUT INSERTED.[EventID]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12);
2025-06-02 10:31:02.625 +05:30 [ERR] An exception occurred in the database while saving changes for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK_FacilityID_123495559212". The conflict occurred in database "HSSE_DB_Latest", table "dbo.mstFacilities", column 'FacilityID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:5343c763-4c57-48d5-a5ef-ef601e918f57
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The INSERT statement conflicted with the FOREIGN KEY constraint "FK_FacilityID_123495559212". The conflict occurred in database "HSSE_DB_Latest", table "dbo.mstFacilities", column 'FacilityID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:5343c763-4c57-48d5-a5ef-ef601e918f57
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
2025-06-02 10:31:20.736 +05:30 [ERR] Method Name : CreateOrUpdateEvent, Error : An error occurred while saving the entity changes. See the inner exception for details.
2025-06-02 10:31:20.743 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:31:20.752 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API) in 34512.3557ms
2025-06-02 10:31:20.753 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API)'
2025-06-02 10:31:20.756 +05:30 [INF] Request finished HTTP/2 POST https://localhost:7231/api/v1/Event/CreateOrUpdateEvent - 400 null application/json; charset=utf-8 34763.5859ms
2025-06-02 10:31:29.548 +05:30 [INF] Request starting HTTP/2 POST https://localhost:7231/api/v1/Event/CreateOrUpdateEvent - application/json 419
2025-06-02 10:31:29.589 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API)'
2025-06-02 10:31:29.592 +05:30 [INF] Route matched with {action = "CreateOrUpdateEvent", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult CreateOrUpdateEvent(Hsse.Data.Dto.Request.CreateEventDto) on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:31:38.456 +05:30 [INF] Executed DbCommand (57ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = DateTime2), @p4='?' (DbType = DateTime2), @p5='?' (Size = 4000), @p6='?' (DbType = Int32), @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?' (Size = 4000), @p10='?' (Size = 4000), @p11='?' (DbType = DateTime2), @p12='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [MstEvents] ([CreatedAt], [CreatedBy], [Description], [EventDateTime], [ExpiryAt], [ExternalLink], [FacilityID], [IsActive], [IsRsvp], [Location], [MediaURL], [ScheduleAt], [Title])
OUTPUT INSERTED.[EventID]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12);
2025-06-02 10:31:50.848 +05:30 [INF] Executed DbCommand (55ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Boolean), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (DbType = Boolean), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [MstAnnouncementReceivers] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, 0),
(@p5, @p6, @p7, @p8, @p9, 1)) AS i ([AnnouncementID], [Delivered], [EventID], [GroupID], [UserID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AnnouncementID], [Delivered], [EventID], [GroupID], [UserID])
VALUES (i.[AnnouncementID], i.[Delivered], i.[EventID], i.[GroupID], i.[UserID])
OUTPUT INSERTED.[ReceiverID], i._Position;
2025-06-02 10:31:50.855 +05:30 [ERR] An exception occurred in the database while saving changes for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The MERGE statement conflicted with the FOREIGN KEY constraint "FK__mstAnnoun__UserI__03F0984C". The conflict occurred in database "HSSE_DB_Latest", table "dbo.mstUsers", column 'UserID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:5343c763-4c57-48d5-a5ef-ef601e918f57
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): The MERGE statement conflicted with the FOREIGN KEY constraint "FK__mstAnnoun__UserI__03F0984C". The conflict occurred in database "HSSE_DB_Latest", table "dbo.mstUsers", column 'UserID'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.Read()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDataReader.Read()
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
ClientConnectionId:5343c763-4c57-48d5-a5ef-ef601e918f57
Error Number:547,State:0,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSet(Int32 startCommandIndex, RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.Consume(RelationalDataReader reader)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.Execute(IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.Execute(IEnumerable`1 commandBatches, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChanges(IList`1 entries)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(IList`1 entriesToSave)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(StateManager stateManager, Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.<>c.<SaveChanges>b__112_0(DbContext _, ValueTuple`2 t)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChanges(Boolean acceptAllChangesOnSuccess)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChanges(Boolean acceptAllChangesOnSuccess)
2025-06-02 10:32:00.676 +05:30 [ERR] Method Name : CreateOrUpdateEvent, Error : An error occurred while saving the entity changes. See the inner exception for details.
2025-06-02 10:32:00.684 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:32:00.686 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API) in 31091.8263ms
2025-06-02 10:32:00.690 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API)'
2025-06-02 10:32:00.692 +05:30 [INF] Request finished HTTP/2 POST https://localhost:7231/api/v1/Event/CreateOrUpdateEvent - 400 null application/json; charset=utf-8 31144.3319ms
2025-06-02 10:32:12.044 +05:30 [INF] Request starting HTTP/2 POST https://localhost:7231/api/v1/Event/CreateOrUpdateEvent - application/json 419
2025-06-02 10:32:12.052 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API)'
2025-06-02 10:32:12.055 +05:30 [INF] Route matched with {action = "CreateOrUpdateEvent", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult CreateOrUpdateEvent(Hsse.Data.Dto.Request.CreateEventDto) on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:32:13.670 +05:30 [INF] Executed DbCommand (48ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = DateTime2), @p4='?' (DbType = DateTime2), @p5='?' (Size = 4000), @p6='?' (DbType = Int32), @p7='?' (DbType = Boolean), @p8='?' (DbType = Boolean), @p9='?' (Size = 4000), @p10='?' (Size = 4000), @p11='?' (DbType = DateTime2), @p12='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [MstEvents] ([CreatedAt], [CreatedBy], [Description], [EventDateTime], [ExpiryAt], [ExternalLink], [FacilityID], [IsActive], [IsRsvp], [Location], [MediaURL], [ScheduleAt], [Title])
OUTPUT INSERTED.[EventID]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12);
2025-06-02 10:32:13.725 +05:30 [INF] Executed DbCommand (49ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = Boolean), @p2='?' (DbType = Int32), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?' (DbType = Int32), @p6='?' (DbType = Boolean), @p7='?' (DbType = Int32), @p8='?' (DbType = Int32), @p9='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [MstAnnouncementReceivers] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, 0),
(@p5, @p6, @p7, @p8, @p9, 1)) AS i ([AnnouncementID], [Delivered], [EventID], [GroupID], [UserID], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([AnnouncementID], [Delivered], [EventID], [GroupID], [UserID])
VALUES (i.[AnnouncementID], i.[Delivered], i.[EventID], i.[GroupID], i.[UserID])
OUTPUT INSERTED.[ReceiverID], i._Position;
2025-06-02 10:32:13.730 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:32:13.736 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API) in 1677.3964ms
2025-06-02 10:32:13.739 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.CreateOrUpdateEvent (Hsse.API)'
2025-06-02 10:32:13.741 +05:30 [INF] Request finished HTTP/2 POST https://localhost:7231/api/v1/Event/CreateOrUpdateEvent - 200 null application/json; charset=utf-8 1697.3102ms
2025-06-02 10:32:35.107 +05:30 [INF] Request starting HTTP/2 DELETE https://localhost:7231/api/v1/Event/DeleteEvent?eventId=19&deletedBy=7 - null null
2025-06-02 10:32:35.112 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.DeleteEvent (Hsse.API)'
2025-06-02 10:32:35.119 +05:30 [INF] Route matched with {action = "DeleteEvent", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult DeleteEvent(Int32, Int32) on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:32:35.208 +05:30 [INF] Executed DbCommand (50ms) [Parameters=[@__eventId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [m].[EventID], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[EventDateTime], [m].[ExpiryAt], [m].[ExternalLink], [m].[FacilityID], [m].[IsActive], [m].[IsRsvp], [m].[Location], [m].[MediaURL], [m].[ScheduleAt], [m].[Title]
FROM [MstEvents] AS [m]
WHERE [m].[EventID] = @__eventId_0
2025-06-02 10:32:35.272 +05:30 [INF] Executed DbCommand (56ms) [Parameters=[@p1='?' (DbType = Int32), @p0='?' (DbType = Boolean)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [MstEvents] SET [IsActive] = @p0
OUTPUT 1
WHERE [EventID] = @p1;
2025-06-02 10:32:35.276 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:32:35.277 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.DeleteEvent (Hsse.API) in 152.9076ms
2025-06-02 10:32:35.279 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.DeleteEvent (Hsse.API)'
2025-06-02 10:32:35.280 +05:30 [INF] Request finished HTTP/2 DELETE https://localhost:7231/api/v1/Event/DeleteEvent?eventId=19&deletedBy=7 - 200 null application/json; charset=utf-8 173.1252ms
2025-06-02 10:32:52.869 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - null null
2025-06-02 10:32:52.912 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:32:52.915 +05:30 [INF] Route matched with {action = "GetEvents", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetEvents() on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:32:53.025 +05:30 [INF] Executed DbCommand (100ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[EventID], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[EventDateTime], [m].[ExpiryAt], [m].[ExternalLink], [m].[FacilityID], [m].[IsActive], [m].[IsRsvp], [m].[Location], [m].[MediaURL], [m].[ScheduleAt], [m].[Title]
FROM [MstEvents] AS [m]
2025-06-02 10:32:53.046 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-02 10:32:53.116 +05:30 [ERR] Method Name : GetEvents, Error : Data is Null. This method or property cannot be called on Null values.
2025-06-02 10:32:53.118 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:32:53.121 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API) in 204.5874ms
2025-06-02 10:32:53.124 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:32:53.132 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - 400 null application/json; charset=utf-8 262.9387ms
2025-06-02 10:33:32.130 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - null null
2025-06-02 10:33:32.176 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:33:32.177 +05:30 [INF] Route matched with {action = "GetEvents", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetEvents() on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:33:43.174 +05:30 [INF] Executed DbCommand (69ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[EventID], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[EventDateTime], [m].[ExpiryAt], [m].[ExternalLink], [m].[FacilityID], [m].[IsActive], [m].[IsRsvp], [m].[Location], [m].[MediaURL], [m].[ScheduleAt], [m].[Title]
FROM [MstEvents] AS [m]
2025-06-02 10:33:43.179 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-02 10:34:32.556 +05:30 [ERR] Method Name : GetEvents, Error : Data is Null. This method or property cannot be called on Null values.
2025-06-02 10:34:32.564 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:34:32.602 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API) in 60422.0121ms
2025-06-02 10:34:32.610 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:34:32.612 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - 400 null application/json; charset=utf-8 60482.5444ms
2025-06-02 10:34:35.336 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - null null
2025-06-02 10:34:35.341 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:34:35.343 +05:30 [INF] Route matched with {action = "GetEvents", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetEvents() on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:34:44.211 +05:30 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[EventID], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[EventDateTime], [m].[ExpiryAt], [m].[ExternalLink], [m].[FacilityID], [m].[IsActive], [m].[IsRsvp], [m].[Location], [m].[MediaURL], [m].[ScheduleAt], [m].[Title]
FROM [MstEvents] AS [m]
2025-06-02 10:34:44.221 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-02 10:36:32.678 +05:30 [ERR] Method Name : GetEvents, Error : Data is Null. This method or property cannot be called on Null values.
2025-06-02 10:36:32.685 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:36:32.689 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API) in 117342.7001ms
2025-06-02 10:36:32.692 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:36:32.693 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - 400 null application/json; charset=utf-8 117357.6096ms
2025-06-02 10:36:57.438 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - null null
2025-06-02 10:36:57.488 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:36:57.496 +05:30 [INF] Route matched with {action = "GetEvents", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetEvents() on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:37:00.059 +05:30 [INF] Executed DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[EventID], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[EventDateTime], [m].[ExpiryAt], [m].[ExternalLink], [m].[FacilityID], [m].[IsActive], [m].[IsRsvp], [m].[Location], [m].[MediaURL], [m].[ScheduleAt], [m].[Title]
FROM [MstEvents] AS [m]
2025-06-02 10:37:00.063 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-02 10:37:00.091 +05:30 [ERR] Method Name : GetEvents, Error : Data is Null. This method or property cannot be called on Null values.
2025-06-02 10:37:00.092 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:37:00.094 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API) in 2594.0256ms
2025-06-02 10:37:00.101 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:37:00.103 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - 400 null application/json; charset=utf-8 2665.3723ms
2025-06-02 10:38:53.648 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - null null
2025-06-02 10:38:53.697 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:38:53.698 +05:30 [INF] Route matched with {action = "GetEvents", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetEvents() on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:38:58.047 +05:30 [INF] Executed DbCommand (57ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[EventID], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[EventDateTime], [m].[ExpiryAt], [m].[ExternalLink], [m].[FacilityID], [m].[IsActive], [m].[IsRsvp], [m].[Location], [m].[MediaURL], [m].[ScheduleAt], [m].[Title]
FROM [MstEvents] AS [m]
2025-06-02 10:38:58.052 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-02 10:46:15.048 +05:30 [ERR] Method Name : GetEvents, Error : Data is Null. This method or property cannot be called on Null values.
2025-06-02 10:46:15.060 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:46:15.094 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API) in 441391.7118ms
2025-06-02 10:46:15.097 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:46:15.100 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - 400 null application/json; charset=utf-8 441452.8361ms
2025-06-02 10:46:16.448 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 10:46:16.568 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 123.039ms
2025-06-02 10:46:16.919 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 10:46:16.939 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 20.5398ms
2025-06-02 10:46:17.024 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 10:46:17.064 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.5615ms
2025-06-02 10:46:18.069 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 10:46:18.158 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 89.4921ms
2025-06-02 10:46:24.076 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - null null
2025-06-02 10:46:24.084 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:46:24.088 +05:30 [INF] Route matched with {action = "GetEvents", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetEvents() on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:46:27.798 +05:30 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[EventID], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[EventDateTime], [m].[ExpiryAt], [m].[ExternalLink], [m].[FacilityID], [m].[IsActive], [m].[IsRsvp], [m].[Location], [m].[MediaURL], [m].[ScheduleAt], [m].[Title]
FROM [MstEvents] AS [m]
2025-06-02 10:46:27.803 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-02 10:46:27.833 +05:30 [ERR] Method Name : GetEvents, Error : Data is Null. This method or property cannot be called on Null values.
2025-06-02 10:46:27.835 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:46:27.839 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API) in 3748.7725ms
2025-06-02 10:46:27.842 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:46:27.844 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - 400 null application/json; charset=utf-8 3767.8656ms
2025-06-02 10:46:32.205 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - null null
2025-06-02 10:46:32.215 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:46:32.221 +05:30 [INF] Route matched with {action = "GetEvents", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetEvents() on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:46:35.720 +05:30 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[EventID], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[EventDateTime], [m].[ExpiryAt], [m].[ExternalLink], [m].[FacilityID], [m].[IsActive], [m].[IsRsvp], [m].[Location], [m].[MediaURL], [m].[ScheduleAt], [m].[Title]
FROM [MstEvents] AS [m]
2025-06-02 10:46:35.729 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-02 10:46:48.214 +05:30 [ERR] Method Name : GetEvents, Error : Data is Null. This method or property cannot be called on Null values.
2025-06-02 10:46:48.222 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:46:48.235 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API) in 16007.4651ms
2025-06-02 10:46:48.236 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:46:48.240 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - 400 null application/json; charset=utf-8 16034.791ms
2025-06-02 10:46:52.061 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - null null
2025-06-02 10:46:52.070 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:46:52.072 +05:30 [INF] Route matched with {action = "GetEvents", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetEvents() on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:46:56.396 +05:30 [INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[EventID], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[EventDateTime], [m].[ExpiryAt], [m].[ExternalLink], [m].[FacilityID], [m].[IsActive], [m].[IsRsvp], [m].[Location], [m].[MediaURL], [m].[ScheduleAt], [m].[Title]
FROM [MstEvents] AS [m]
2025-06-02 10:46:56.401 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method436(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-02 10:47:56.241 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 10:47:56.561 +05:30 [INF] Now listening on: https://localhost:7231
2025-06-02 10:47:56.564 +05:30 [INF] Now listening on: http://localhost:5133
2025-06-02 10:47:56.657 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 10:47:56.659 +05:30 [INF] Hosting environment: Development
2025-06-02 10:47:56.661 +05:30 [INF] Content root path: D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API
2025-06-02 10:47:58.218 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 10:47:58.812 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 10:47:58.814 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 10:47:58.825 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 616.6232ms
2025-06-02 10:47:58.872 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 60.0917ms
2025-06-02 10:47:58.968 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 153.9564ms
2025-06-02 10:47:59.611 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 10:47:59.664 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 53.2974ms
2025-06-02 10:48:07.814 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - null null
2025-06-02 10:48:07.916 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:48:07.951 +05:30 [INF] Route matched with {action = "GetEvents", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetEvents() on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 10:48:13.251 +05:30 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[EventID], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[EventDateTime], [m].[ExpiryAt], [m].[ExternalLink], [m].[FacilityID], [m].[IsActive], [m].[IsRsvp], [m].[Location], [m].[MediaURL], [m].[ScheduleAt], [m].[Title]
FROM [MstEvents] AS [m]
2025-06-02 10:48:15.652 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 10:48:15.692 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API) in 7733.6925ms
2025-06-02 10:48:15.696 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 10:48:15.708 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - 200 null application/json; charset=utf-8 7894.8582ms
2025-06-02 11:40:31.785 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 11:40:32.107 +05:30 [INF] Now listening on: https://localhost:7231
2025-06-02 11:40:32.109 +05:30 [INF] Now listening on: http://localhost:5133
2025-06-02 11:40:32.214 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 11:40:32.220 +05:30 [INF] Hosting environment: Development
2025-06-02 11:40:32.222 +05:30 [INF] Content root path: D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API
2025-06-02 11:40:32.877 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 11:40:33.217 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 352.0879ms
2025-06-02 11:40:33.239 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 11:40:33.241 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 11:40:33.266 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 26.9187ms
2025-06-02 11:40:33.354 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 112.8788ms
2025-06-02 11:40:33.967 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 11:40:34.046 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 79.5009ms
2025-06-02 11:40:48.446 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - null null
2025-06-02 11:40:50.152 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 11:40:50.183 +05:30 [INF] Route matched with {action = "GetEvents", controller = "Event"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetEvents() on controller Hsse.API.Controllers.v1.EventController (Hsse.API).
2025-06-02 11:40:58.874 +05:30 [INF] Executed DbCommand (118ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[EventID], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[EventDateTime], [m].[ExpiryAt], [m].[ExternalLink], [m].[FacilityID], [m].[IsActive], [m].[IsRsvp], [m].[Location], [m].[MediaURL], [m].[ScheduleAt], [m].[Title]
FROM [MstEvents] AS [m]
2025-06-02 11:40:59.012 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 11:40:59.089 +05:30 [INF] Executed action Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API) in 8894.9004ms
2025-06-02 11:40:59.092 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.EventController.GetEvents (Hsse.API)'
2025-06-02 11:40:59.108 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Event/GetEvents - 200 null application/json; charset=utf-8 10662.5247ms
2025-06-02 11:41:11.971 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetInspections - null null
2025-06-02 11:41:11.981 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.InspectionController.GetInspections (Hsse.API)'
2025-06-02 11:41:11.990 +05:30 [INF] Route matched with {action = "GetInspections", controller = "Inspection"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetInspections() on controller Hsse.API.Controllers.v1.InspectionController (Hsse.API).
2025-06-02 11:41:12.080 +05:30 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[InspectionId], [m].[CreatedAt], [m].[CreatedBy], [m].[Description], [m].[FacilityID], [m].[InspectionDate], [m].[ModifiedAt], [m].[ReferenceNo], [m].[Title]
FROM [MstInspections] AS [m]
2025-06-02 11:41:12.152 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 11:41:12.162 +05:30 [INF] Executed action Hsse.API.Controllers.v1.InspectionController.GetInspections (Hsse.API) in 169.9582ms
2025-06-02 11:41:12.165 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.InspectionController.GetInspections (Hsse.API)'
2025-06-02 11:41:12.168 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetInspections - 200 null application/json; charset=utf-8 197.0492ms
2025-06-02 11:41:22.117 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - null null
2025-06-02 11:41:22.139 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:41:22.142 +05:30 [INF] Route matched with {action = "GetActionParties", controller = "Inspection"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetActionParties() on controller Hsse.API.Controllers.v1.InspectionController (Hsse.API).
2025-06-02 11:41:22.290 +05:30 [ERR] Failed executing DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ActionPartyId], [m].[CreatedAt], [m].[CreatedBy], [m].[FacilityID], [m].[IsActive], [m].[ModifiedAt], [m].[ModifiedBy], [m].[Name], [m].[Observation], [m].[ObservationMediaUrl], [m].[RecommendationMediaUrl]
FROM [MstActionParty] AS [m]
2025-06-02 11:41:22.327 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:4a1c7cdd-1adf-4f2f-a715-2ddc869cb5fb
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:4a1c7cdd-1adf-4f2f-a715-2ddc869cb5fb
Error Number:207,State:1,Class:16
2025-06-02 11:41:22.460 +05:30 [ERR] Method Name : GetActionParties, Error : Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
2025-06-02 11:41:22.462 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 11:41:22.465 +05:30 [INF] Executed action Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API) in 320.1867ms
2025-06-02 11:41:22.472 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:41:22.473 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - 400 null application/json; charset=utf-8 356.4562ms
2025-06-02 11:41:43.918 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - null null
2025-06-02 11:41:43.963 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:41:43.970 +05:30 [INF] Route matched with {action = "GetActionParties", controller = "Inspection"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetActionParties() on controller Hsse.API.Controllers.v1.InspectionController (Hsse.API).
2025-06-02 11:41:46.813 +05:30 [ERR] Failed executing DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ActionPartyId], [m].[CreatedAt], [m].[CreatedBy], [m].[FacilityID], [m].[IsActive], [m].[ModifiedAt], [m].[ModifiedBy], [m].[Name], [m].[Observation], [m].[ObservationMediaUrl], [m].[RecommendationMediaUrl]
FROM [MstActionParty] AS [m]
2025-06-02 11:41:46.818 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:4a1c7cdd-1adf-4f2f-a715-2ddc869cb5fb
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:4a1c7cdd-1adf-4f2f-a715-2ddc869cb5fb
Error Number:207,State:1,Class:16
2025-06-02 11:41:55.306 +05:30 [ERR] Method Name : GetActionParties, Error : Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
2025-06-02 11:41:55.317 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 11:41:55.319 +05:30 [INF] Executed action Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API) in 11346.9051ms
2025-06-02 11:41:55.322 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:41:55.324 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - 400 null application/json; charset=utf-8 11406.0624ms
2025-06-02 11:41:57.772 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - null null
2025-06-02 11:41:57.778 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:41:57.780 +05:30 [INF] Route matched with {action = "GetActionParties", controller = "Inspection"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetActionParties() on controller Hsse.API.Controllers.v1.InspectionController (Hsse.API).
2025-06-02 11:42:06.691 +05:30 [ERR] Failed executing DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ActionPartyId], [m].[CreatedAt], [m].[CreatedBy], [m].[FacilityID], [m].[IsActive], [m].[ModifiedAt], [m].[ModifiedBy], [m].[Name], [m].[Observation], [m].[ObservationMediaUrl], [m].[RecommendationMediaUrl]
FROM [MstActionParty] AS [m]
2025-06-02 11:42:06.697 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:4a1c7cdd-1adf-4f2f-a715-2ddc869cb5fb
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:4a1c7cdd-1adf-4f2f-a715-2ddc869cb5fb
Error Number:207,State:1,Class:16
2025-06-02 11:43:05.748 +05:30 [ERR] Method Name : GetActionParties, Error : Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
2025-06-02 11:43:05.791 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 11:43:05.802 +05:30 [INF] Executed action Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API) in 68017.6386ms
2025-06-02 11:43:05.808 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:43:05.809 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - 400 null application/json; charset=utf-8 68036.8832ms
2025-06-02 11:43:06.893 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 11:43:06.970 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 66.1007ms
2025-06-02 11:43:07.276 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 11:43:07.364 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 11:43:07.698 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 421.8835ms
2025-06-02 11:43:07.842 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 477.3199ms
2025-06-02 11:43:08.509 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 11:43:08.549 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 39.7622ms
2025-06-02 11:43:14.959 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - null null
2025-06-02 11:43:14.966 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:43:14.972 +05:30 [INF] Route matched with {action = "GetActionParties", controller = "Inspection"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetActionParties() on controller Hsse.API.Controllers.v1.InspectionController (Hsse.API).
2025-06-02 11:43:17.943 +05:30 [ERR] Failed executing DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ActionPartyId], [m].[CreatedAt], [m].[CreatedBy], [m].[FacilityID], [m].[IsActive], [m].[ModifiedAt], [m].[ModifiedBy], [m].[Name], [m].[Observation], [m].[ObservationMediaUrl], [m].[RecommendationMediaUrl]
FROM [MstActionParty] AS [m]
2025-06-02 11:43:17.947 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:4a1c7cdd-1adf-4f2f-a715-2ddc869cb5fb
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:4a1c7cdd-1adf-4f2f-a715-2ddc869cb5fb
Error Number:207,State:1,Class:16
2025-06-02 11:43:17.982 +05:30 [ERR] Method Name : GetActionParties, Error : Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
2025-06-02 11:43:17.984 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 11:43:17.988 +05:30 [INF] Executed action Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API) in 3013.4395ms
2025-06-02 11:43:17.990 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:43:17.994 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - 400 null application/json; charset=utf-8 3035.2489ms
2025-06-02 11:43:23.235 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - null null
2025-06-02 11:43:23.250 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:43:23.252 +05:30 [INF] Route matched with {action = "GetActionParties", controller = "Inspection"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetActionParties() on controller Hsse.API.Controllers.v1.InspectionController (Hsse.API).
2025-06-02 11:43:30.500 +05:30 [ERR] Failed executing DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ActionPartyId], [m].[CreatedAt], [m].[CreatedBy], [m].[FacilityID], [m].[IsActive], [m].[ModifiedAt], [m].[ModifiedBy], [m].[Name], [m].[Observation], [m].[ObservationMediaUrl], [m].[RecommendationMediaUrl]
FROM [MstActionParty] AS [m]
2025-06-02 11:43:30.505 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:4a1c7cdd-1adf-4f2f-a715-2ddc869cb5fb
Error Number:207,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
ClientConnectionId:4a1c7cdd-1adf-4f2f-a715-2ddc869cb5fb
Error Number:207,State:1,Class:16
2025-06-02 11:43:35.877 +05:30 [ERR] Method Name : GetActionParties, Error : Invalid column name 'CreatedAt'.
Invalid column name 'CreatedBy'.
Invalid column name 'ModifiedAt'.
Invalid column name 'ModifiedBy'.
2025-06-02 11:43:35.880 +05:30 [INF] Executing BadRequestObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 11:43:35.882 +05:30 [INF] Executed action Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API) in 12625.5841ms
2025-06-02 11:43:35.885 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:43:35.886 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - 400 null application/json; charset=utf-8 12651.9386ms
2025-06-02 11:44:57.076 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 11:44:57.409 +05:30 [INF] Now listening on: https://localhost:7231
2025-06-02 11:44:57.412 +05:30 [INF] Now listening on: http://localhost:5133
2025-06-02 11:44:57.511 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 11:44:57.513 +05:30 [INF] Hosting environment: Development
2025-06-02 11:44:57.514 +05:30 [INF] Content root path: D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API
2025-06-02 11:44:59.261 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 11:44:59.769 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 531.4193ms
2025-06-02 11:44:59.802 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 11:44:59.814 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 11:44:59.866 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 64.4908ms
2025-06-02 11:44:59.975 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 161.1697ms
2025-06-02 11:45:00.611 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 11:45:00.711 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 100.8315ms
2025-06-02 11:45:06.941 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - null null
2025-06-02 11:45:07.055 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:45:07.086 +05:30 [INF] Route matched with {action = "GetActionParties", controller = "Inspection"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetActionParties() on controller Hsse.API.Controllers.v1.InspectionController (Hsse.API).
2025-06-02 11:45:11.332 +05:30 [INF] Executed DbCommand (87ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ActionPartyId], [m].[FacilityID], [m].[IsActive], [m].[Observation], [m].[ObservationMediaUrl], [m].[RecommendationMediaUrl], [m].[created_at], [m].[created_by], [m].[modified_at], [m].[modified_by], [m].[name]
FROM [MstActionParty] AS [m]
2025-06-02 11:45:11.514 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 11:45:11.562 +05:30 [INF] Executed action Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API) in 4465.866ms
2025-06-02 11:45:11.564 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.InspectionController.GetActionParties (Hsse.API)'
2025-06-02 11:45:11.579 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Inspection/GetActionParties - 200 null application/json; charset=utf-8 4639.5348ms
2025-06-02 11:45:26.144 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post/GetPosts - null null
2025-06-02 11:45:26.156 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-06-02 11:45:26.161 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts() on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-06-02 11:45:26.242 +05:30 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
2025-06-02 11:45:26.281 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 11:45:26.310 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 146.5261ms
2025-06-02 11:45:26.317 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-06-02 11:45:26.321 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post/GetPosts - 200 null application/json; charset=utf-8 177.719ms
2025-06-02 11:45:37.464 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post/GetPostCategories - null null
2025-06-02 11:45:37.481 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPostCategories (Hsse.API)'
2025-06-02 11:45:37.484 +05:30 [INF] Route matched with {action = "GetPostCategories", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPostCategories() on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-06-02 11:45:37.608 +05:30 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[CatID], [m].[CategoryName], [m].[CreatedAt]
FROM [MstPostCategories] AS [m]
2025-06-02 11:45:37.624 +05:30 [INF] Executing OkObjectResult, writing value of type 'Hsse.Data.Dto.Response.ResponseDetails'.
2025-06-02 11:45:37.628 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPostCategories (Hsse.API) in 140.5199ms
2025-06-02 11:45:37.631 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPostCategories (Hsse.API)'
2025-06-02 11:45:37.632 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post/GetPostCategories - 200 null application/json; charset=utf-8 168.3408ms
2025-06-02 12:55:29.237 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 12:55:29.770 +05:30 [INF] Now listening on: https://localhost:7231
2025-06-02 12:55:29.773 +05:30 [INF] Now listening on: http://localhost:5133
2025-06-02 12:55:29.922 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 12:55:30.023 +05:30 [INF] Hosting environment: Development
2025-06-02 12:55:30.029 +05:30 [INF] Content root path: D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API
2025-06-02 12:55:30.655 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 12:55:31.043 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 400.5722ms
2025-06-02 12:55:31.058 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:55:31.060 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 12:55:31.081 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 23.6589ms
2025-06-02 12:55:31.204 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 144.5227ms
2025-06-02 12:55:31.801 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 12:55:31.868 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 66.8605ms
2025-06-02 15:16:09.271 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 15:16:09.727 +05:30 [INF] Now listening on: https://localhost:7231
2025-06-02 15:16:09.729 +05:30 [INF] Now listening on: http://localhost:5133
2025-06-02 15:16:09.874 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 15:16:09.877 +05:30 [INF] Hosting environment: Development
2025-06-02 15:16:09.878 +05:30 [INF] Content root path: D:\UEMS\Documents\CopyProject\hsse-mobile-api\HsseAPI\Hsse.API
2025-06-02 15:16:10.886 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-06-02 15:16:11.359 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 15:16:11.371 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-06-02 15:16:11.373 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 498.6036ms
2025-06-02 15:16:11.393 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 34.0712ms
2025-06-02 15:16:11.574 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 205.9688ms
2025-06-02 15:16:12.155 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-06-02 15:16:12.220 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 65.601ms
