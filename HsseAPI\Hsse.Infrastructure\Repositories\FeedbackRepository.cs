using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class FeedbackRepository : IFeedbackRepository
    {
        private readonly MasterDBContext _MasterDBContext;

        public FeedbackRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }

        public long CreateFeedback(CreateFeedbackDto createFeedbackDto)
        {
            var newFeedback = new MstFeedback
            {
                UserID = createFeedbackDto.UserID,
                FacilityID = createFeedbackDto.FacilityID,
                FeedbackType = createFeedbackDto.FeedbackType,
                Subject = createFeedbackDto.Subject,
                Description = createFeedbackDto.Description,
                Priority = createFeedbackDto.Priority,
                Status = "Open",
                AttachmentURL = createFeedbackDto.AttachmentURL,
                CreatedAt = DateTime.Now,
                IsDeleted = false
            };

            _MasterDBContext.MstFeedback.Add(newFeedback);
            _MasterDBContext.SaveChanges();

            return newFeedback.FeedbackID;
        }

        public List<MstFeedback> GetAllFeedback()
        {
            return _MasterDBContext.MstFeedback
                .Where(f => !f.IsDeleted)
                .OrderByDescending(f => f.CreatedAt)
                .ToList();
        }

        public List<MstFeedback> GetFeedbackByFacility(int facilityId)
        {
            return _MasterDBContext.MstFeedback
                .Where(f => f.FacilityID == facilityId && !f.IsDeleted)
                .OrderByDescending(f => f.CreatedAt)
                .ToList();
        }

        public MstFeedback GetFeedbackById(int feedbackId)
        {
            return _MasterDBContext.MstFeedback
                .FirstOrDefault(f => f.FeedbackID == feedbackId && !f.IsDeleted);
        }

        public int UpdateFeedbackStatus(int feedbackId, string status, string adminResponse, int respondedBy)
        {
            var feedback = _MasterDBContext.MstFeedback
                .FirstOrDefault(f => f.FeedbackID == feedbackId && !f.IsDeleted);

            if (feedback == null)
                return 0;

            feedback.Status = status;
            feedback.AdminResponse = adminResponse;
            feedback.ResponseDate = DateTime.Now;
            feedback.AssignedTo = respondedBy;
            feedback.UpdatedAt = DateTime.Now;

            _MasterDBContext.SaveChanges();
            return 1;
        }
    }
}
