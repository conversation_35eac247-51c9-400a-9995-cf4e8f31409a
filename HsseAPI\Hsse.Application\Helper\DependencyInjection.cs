﻿using Hsse.Application.IServices;
using Hsse.Application.Services;
using Hsse.Infrastructure.IRepositories;
using Hsse.Infrastructure.Repositories;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Helper
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplicationDI(this IServiceCollection services)
        {
            services.AddScoped<IPostService, PostService>();
            services.AddScoped<IAnnouncementService, AnnouncementService>();
            services.AddScoped<IInspectionService, InspectionService>();
            services.AddScoped<IEventService, EventService>();
            services.AddScoped<IFeedbackService, FeedbackService>();
            services.AddScoped<IDocumentService, DocumentService>();

            return services;
        }
    }
}
