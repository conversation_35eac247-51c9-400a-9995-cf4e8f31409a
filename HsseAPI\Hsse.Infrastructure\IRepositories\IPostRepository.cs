﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IPostRepository
    {
        long CreatePost(CreatePostDto createPostDto);
        List<MstPosts> GetPosts();
        MstPosts GetPostById(int postId);
        List<MstPosts> GetUnresolvedPosts();
        List<MstPostCategories> GetPostCategories();
        int CreateOrUpdateLikes(CreateLikeDto createLikeDto);
        int ClosePost(int postId, int closedBy);
        int CloseObservation(CloseObservationDto closeObservationDto);
        int DeletePost(int postId, int deletedBy);
        int CreateOrUpdateComment(CreateCommentDto createCommentDto);
        int SendDailyReminders();
    }
}
