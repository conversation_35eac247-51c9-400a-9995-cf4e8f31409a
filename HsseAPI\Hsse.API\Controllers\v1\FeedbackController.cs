using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class FeedbackController : ControllerBase
    {
        private readonly IFeedbackService _IFeedbackService;
        private readonly ILogger<FeedbackController> _logger;

        public FeedbackController(IFeedbackService feedbackService, IConfiguration configuration, ILogger<FeedbackController> logger)
        {
            _logger = logger;
            _IFeedbackService = feedbackService;
        }

        [HttpPost]
        public IActionResult CreateFeedback([FromBody] CreateFeedbackDto createFeedbackDto)
        {
            var response = new ResponseDetails();
            try
            {
                long result = _IFeedbackService.CreateFeedback(createFeedbackDto);
                response.Status = 1;
                response.Message = "Feedback submitted successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while submitting feedback";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpGet]
        public IActionResult GetAllFeedback()
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IFeedbackService.GetAllFeedback();
                response.Status = 1;
                response.Message = "Feedback retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving feedback";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpGet("facility/{facilityId}")]
        public IActionResult GetFeedbackByFacility(int facilityId)
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IFeedbackService.GetFeedbackByFacility(facilityId);
                response.Status = 1;
                response.Message = "Feedback retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving feedback";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpGet("{id}")]
        public IActionResult GetFeedbackById(int id)
        {
            var response = new ResponseDetails();
            try
            {
                var result = _IFeedbackService.GetFeedbackById(id);
                response.Status = 1;
                response.Message = "Feedback retrieved successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while retrieving feedback";
                response.Result = null;
                return BadRequest(response);
            }
        }

        [HttpPut("{id}/status")]
        public IActionResult UpdateFeedbackStatus(int id, [FromQuery] string status, [FromQuery] string adminResponse, [FromQuery] int respondedBy)
        {
            var response = new ResponseDetails();
            try
            {
                int result = _IFeedbackService.UpdateFeedbackStatus(id, status, adminResponse, respondedBy);
                response.Status = 1;
                response.Message = "Feedback status updated successfully";
                response.Result = result;
                return Ok(response);
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                response.Status = 0;
                response.Message = "Error occurred while updating feedback status";
                response.Result = null;
                return BadRequest(response);
            }
        }
    }
}
