﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstPermission
    {
        [Key]
        public int PermissionID { get; set; }
        public int? ParentMenuId { get; set; }
        public string? MenuName { get; set; }
        public string? ControllerName { get; set; }
        public string? ActionName { get; set; }
        public string? AreaName { get; set; }
        public string? RouteParams { get; set; }
        public string? Icon { get; set; }
        public int? OrderNo { get; set; }
        public bool? IsActive { get; set; }
    }
}
