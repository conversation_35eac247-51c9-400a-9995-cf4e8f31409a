﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.IServices
{
    public interface IAnnouncementService
    {
        long CreateOrUpdateAnnouncement(CreateAnnouncementDto createAnnouncementDto);
        int CreateOrUpdateCategory(CreateAnnouncementCategoryDto createCategoryDto);
        List<MstAnnouncementCategory> GetCategories();
        List<MstAnnouncements> GetAnnouncements();
    }
}
