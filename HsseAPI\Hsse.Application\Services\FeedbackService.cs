using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class FeedbackService : IFeedbackService
    {
        private readonly IFeedbackRepository _IFeedbackRepository;

        public FeedbackService(IFeedbackRepository feedbackRepository)
        {
            _IFeedbackRepository = feedbackRepository;
        }

        public long CreateFeedback(CreateFeedbackDto createFeedbackDto)
        {
            var result = _IFeedbackRepository.CreateFeedback(createFeedbackDto);
            return result;
        }

        public List<MstFeedback> GetAllFeedback()
        {
            var result = _IFeedbackRepository.GetAllFeedback();
            return result;
        }

        public List<MstFeedback> GetFeedbackByFacility(int facilityId)
        {
            var result = _IFeedbackRepository.GetFeedbackByFacility(facilityId);
            return result;
        }

        public MstFeedback GetFeedbackById(int feedbackId)
        {
            var result = _IFeedbackRepository.GetFeedbackById(feedbackId);
            return result;
        }

        public int UpdateFeedbackStatus(int feedbackId, string status, string adminResponse, int respondedBy)
        {
            var result = _IFeedbackRepository.UpdateFeedbackStatus(feedbackId, status, adminResponse, respondedBy);
            return result;
        }
    }
}
