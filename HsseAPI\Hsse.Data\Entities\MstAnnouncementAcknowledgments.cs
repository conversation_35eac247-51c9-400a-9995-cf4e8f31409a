using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstAnnouncementAcknowledgments
    {
        [Key]
        public int AcknowledgmentID { get; set; }
        public int AnnouncementID { get; set; }
        public int UserID { get; set; }
        public bool IsAcknowledged { get; set; }
        public DateTime? AcknowledgedAt { get; set; }
    }
}
