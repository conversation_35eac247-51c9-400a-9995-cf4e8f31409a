﻿using Hsse.Infrastructure.IRepositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.WindowsAzure.Storage.Auth;
using Microsoft.WindowsAzure.Storage.Blob;
using Microsoft.WindowsAzure.Storage;
using System.Reflection;

public class StorageRepository : IStorageRepository
{
    private readonly IConfiguration? _configuration;
    private readonly ILogger<StorageRepository> _logger;
    private readonly CloudBlobContainer _container;
    private readonly string? _customerName;

    public StorageRepository(IConfiguration configuration, ILogger<StorageRepository> logger)
    {
        _configuration = configuration;
        _logger = logger;

        string? accountName = _configuration?.GetSection("AzureConnectionConfig:AccountName").Value;
        string? accountKey = _configuration?.GetSection("AzureConnectionConfig:AccountKey").Value;
        string? containerName = _configuration?.GetSection("AzureConnectionConfig:ContainerName").Value;
        _customerName = _configuration?.GetSection("AzureConnectionConfig:CustomerName").Value;

        if (string.IsNullOrEmpty(accountName) || string.IsNullOrEmpty(accountKey) || string.IsNullOrEmpty(containerName))
            throw new InvalidOperationException("Azure Blob Storage configuration is invalid.");

        StorageCredentials creds = new StorageCredentials(accountName, accountKey);
        CloudStorageAccount account = new CloudStorageAccount(creds, useHttps: true);
        CloudBlobClient client = account.CreateCloudBlobClient();
        _container = client.GetContainerReference(containerName);
    }

    private CloudBlockBlob GetBlobReference(string? module, string? imageName)
    {
        if (string.IsNullOrEmpty(module) || string.IsNullOrEmpty(imageName))
            throw new ArgumentException("Module and Image Name cannot be null or empty.");

        return _container.GetBlockBlobReference($"{_customerName}/{module}/{imageName}");
    }

    public async Task UploadBlob(string? base64String, string? imageName, string? module)
    {
        try
        {
            if (string.IsNullOrEmpty(base64String))
                throw new ArgumentException("Base64 string cannot be null or empty.");

            await _container.CreateIfNotExistsAsync();

            CloudBlockBlob blob = GetBlobReference(module, imageName);

            string? base64Data = base64String.Split(',').LastOrDefault();
            byte[] imageBytes = Convert.FromBase64String(base64Data ?? string.Empty);

            using (Stream stream = new MemoryStream(imageBytes))
            {
                await blob.UploadFromStreamAsync(stream);
            }
        }
        catch (Exception ex)
        {
            LogError(ex);
            throw;
        }
    }

    public async Task<bool> CheckBlobExists(string? imageName, string? module)
    {
        try
        {
            CloudBlockBlob blob = GetBlobReference(module, imageName);
            return await blob.ExistsAsync();
        }
        catch (Exception ex)
        {
            LogError(ex);
            throw;
        }
    }

    public async Task<bool> DeleteBlob(string? imageName, string? module)
    {
        try
        {
            CloudBlockBlob blob = GetBlobReference(module, imageName);
            return await blob.DeleteIfExistsAsync();
        }
        catch (Exception ex)
        {
            LogError(ex);
            throw;
        }
    }

    private void LogError(Exception ex)
    {
        var methodName = MethodBase.GetCurrentMethod()?.Name;
        _logger.LogError(ex, "Method: {MethodName}, Error: {ErrorMessage}", methodName, ex.Message);
    }
}
